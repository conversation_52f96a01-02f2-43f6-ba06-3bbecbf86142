<template>
  <div class="prison-visualization" :class="{ 'fullscreen-mode': isFullscreen }">
    <!-- 监区名称标题 -->
    <div class="visualization-header">
      <div class="header-title">
        <div class="title-glow">{{ prisonData.departName }}</div>
       
      </div>
      <div class="header-actions">
        <div class="header-time">{{ currentTime }}</div>
        <a-button
          v-if="!isFullscreen"
          type="primary"
          class="fullscreen-btn"
          @click="toggleFullscreen"
          :icon="h(FullscreenOutlined)"
        >
          全屏显示
        </a-button>
        <a-button
          v-else
          type="default"
          class="exit-fullscreen-btn"
          @click="exitFullscreen"
          :icon="h(FullscreenExitOutlined)"
        >
          退出全屏
        </a-button>
      </div>
    </div>

    <!-- 今日巡更人员信息 -->
    <div class="staff-section">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="empty-state">
        <div class="empty-icon">
          <Icon icon="ant-design:loading-outlined" class="loading-icon" />
        </div>
        <div class="empty-text">正在加载数据...</div>
      </div>

      <!-- 无权限或无数据状态 -->
      <div v-else-if="!hasPermission || isEmpty" class="empty-state">
        <div class="empty-icon">
          <Icon icon="ant-design:warning-outlined" />
        </div>
        <div class="empty-text">未拥有权限/数据为空</div>
        <div class="empty-description">请联系管理员获取巡更管理权限或检查数据配置</div>
      </div>

      <!-- 正常数据显示 -->
      <div v-else class="sections-container">
        <div
          v-for="section in prisonData.lines"
          :key="section.id"
          class="section-group"
          :class="{ 'section-has-many-staff': section.cardList.length > 3 }"
        >
          <div class="section-name">{{ section.name }}</div>
          <div class="staff-cards" :class="{ 'has-many-staff': section.cardList.length > 3 }">
            <div
              v-for="staff in section.cardList"
              :key="staff.id"
              class="staff-card"
              :class="{ 'active': staff.online }"
            >
              <div class="staff-avatar">
                <img :src="getFileAccessHttpUrl(staff.patrolUserImage) || defaultAvatar" :alt="staff.patrolUserName" />
                <!-- 状态指示器：online=true显示巡更中(绿色+动画)，false显示离线(灰色) -->
                <div class="status-indicator" :class="{ 'on-duty': staff.online, 'offline': !staff.online }"></div>
              </div>
              <div class="staff-info">
                <div class="staff-name">{{ staff.patrolUserName }}</div>
                <div class="staff-card-id">{{ staff.cardCode }}</div>
                <!-- 状态文本：online=true显示"巡更中"，false显示"离线" -->
                <div class="staff-status">{{ staff.online ? '巡更中' : '离线' }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 实时巡更计划状态 -->
    <div class="patrol-plans-section">
      <div class="section-title">
        <div class="title-line"></div>
        <span>实时巡更记录</span>
        <div class="title-line"></div>
      </div>

      <div class="plans-container">
        <!-- 加载状态 -->
        <div v-if="isLoading" class="empty-state">
          <div class="empty-icon">
            <Icon icon="ant-design:loading-outlined" class="loading-icon" />
          </div>
          <div class="empty-text">正在加载巡更记录...</div>
        </div>

        <!-- 无权限或无数据状态 -->
        <div v-else-if="!hasPermission || isEmpty || prisonData.plans.length === 0" class="empty-state">
          <div class="empty-icon">
            <Icon icon="ant-design:warning-outlined" />
          </div>
          <div class="empty-text">未拥有权限/数据为空</div>
          <div class="empty-description">请联系管理员获取巡更管理权限或检查数据配置</div>
        </div>

        <!-- 正常数据显示 -->
        <div v-else class="plans-scroll" ref="plansScrollRef">
          <div
            v-for="plan in prisonData.plans"
            :key="plan.id"
            class="plan-item"
            :class="`status-${plan.status}`"
          >
            <!-- 计划基本信息 - 一行显示 -->
            <div class="plan-summary">
              <div class="summary-item">
                <span class="label">计划:</span>
                <span class="value">{{ plan.name || '巡更计划' }}</span>
              </div>
              <div class="summary-item">
                <span class="label">监区:</span>
                <span class="value">{{ plan.lineName }}</span>
              </div>
              <div class="summary-item">
                <span class="label">巡更者:</span>
                <div class="staff-info">
                  <img :src="getFileAccessHttpUrl(plan.patrolUserImage) || defaultAvatar" :alt="plan.patrolUserName" class="staff-avatar-tiny" />
                  <span class="value">{{ plan.patrolUserName || "未知" }}</span>
                </div>
              </div>
              <div class="summary-item">
                <span class="label">开始:</span>
                <span class="value">{{ plan.startTime }}</span>
              </div>
              <div class="summary-item" v-if="plan.endTime">
                <span class="label">结束:</span>
                <span class="value">{{ plan.endTime }}</span>
              </div>
              <div class="summary-item" v-if="plan.status === '2' && plan.patrolDuration">
                <span class="label">用时:</span>
                <span class="value">{{ plan.patrolDuration }}</span>
              </div>
              <div class="plan-status-badge" :class="plan.status">
                {{ getStatusText(plan.status) }}
              </div>
            </div>

            <!-- 巡更点状态 - 铺满显示 -->
            <div class="patrol-points">
              <div
                v-for="(point, index) in plan.planCardList"
                :key="point.id"
                class="patrol-point"
                :class="[
                  `status-${point.status}`,
                  {
                    'is-current': plan.status === 'in-progress' && point.status === 'current',
                    'is-next': plan.status === 'in-progress' && point.status === 'pending' && isNextPoint(plan, index)
                  }
                ]"
                :style="{ width: `calc(${100 / plan.planCardList.length}% - ${(plan.planCardList.length - 1) * 8 / plan.planCardList.length}px)` }"
              >
                <div class="point-icon" :class="`icon-${point.status}`">
                  <Icon :icon="getPointIcon(point.status)" />
                </div>
                <div class="point-info">
                  <div class="point-name">{{ point.cardName }}</div>
                  <div class="point-time" v-if="point.time">
                    {{ point.time }}
                  </div>
                </div>
                <!-- 连接线 -->
                <div v-if="index < plan.planCardList.length - 1" class="point-connector">
                  <div class="connector-line"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, computed, h } from 'vue';
import { getPrisonVisualizationData, type PrisonVisualizationData, type PatrolPlan } from './PrisonVisualization.api';
import { FullscreenOutlined, FullscreenExitOutlined } from '@ant-design/icons-vue';
import { Icon } from '/@/components/Icon';
import dayjs from 'dayjs';
import { getFileAccessHttpUrl } from '/@/utils/common/compUtils';

// 响应式数据
const currentTime = ref('');
const prisonData = ref<PrisonVisualizationData>({
  prisonName: '',
  sections: [],
  totalStatistics: {
    totalStaff: 0,
    onDutyStaff: 0,
    totalPlans: 0,
    inProgressPlans: 0,
    completedPlans: 0,
    missedPlans: 0
  }
});
const plansScrollRef = ref();
const isFullscreen = ref(false);
const hasPermission = ref(true); // 权限状态
const isLoading = ref(true); // 加载状态
const isEmpty = ref(false); // 数据为空状态

// 默认头像
const defaultAvatar = '/src/assets/images/ai/avatar.jpg';

// 定时器
let timeTimer: NodeJS.Timeout;
let dataTimer: NodeJS.Timeout;

// 计算属性 - 排序后的巡更计划
const sortedPatrolPlans = computed(() => {
  // 获取状态优先级
  // 数字状态码排序：1进行中 > 0待巡更 > 2已完成
  // 字符串状态排序：进行中 > 待巡更 > 已完成 > 已漏巡
  const getStatusPriority = (status: number | string) => {
    if (typeof status === 'number') {
      switch (status) {
        case 1: return 1; // 进行中优先级最高
        case 0: return 2; // 待巡更次之
        case 2: return 3; // 已完成最后
        default: return 4; // 其他状态
      }
    }
    // 字符串状态兼容处理
    const stringOrder = { 'in-progress': 1, 'pending': 2, 'completed': 3, 'missed': 4 };
    return stringOrder[status] || 4;
  };

  // 尝试从不同的数据结构中获取计划数据
  let plans: any[] = [];

  // 优先使用 prisonData.plans
  if ((prisonData.value as any).plans && Array.isArray((prisonData.value as any).plans)) {
    plans = (prisonData.value as any).plans;
  }
  // 兼容旧的数据结构 prisonData.sections[].patrolPlans
  else if (prisonData.value.sections && Array.isArray(prisonData.value.sections)) {
    prisonData.value.sections.forEach(section => {
      if (section.patrolPlans && Array.isArray(section.patrolPlans)) {
        plans.push(...section.patrolPlans);
      }
    });
  }

  // 排序并返回
  return plans.sort((a: any, b: any) => {
    return getStatusPriority(a.status) - getStatusPriority(b.status);
  });
});

// 获取状态文本（支持数字状态码和字符串状态）
const getStatusText = (status: string | number) => {
  // 数字状态码映射：0=待巡更，1=进行中，2=已完成
  const numericStatusMap = {
    0: '待巡更',
    1: '进行中',
    2: '已完成'
  };

  // 字符串状态映射（保持向后兼容）
  const stringStatusMap = {
    '0': '待巡更',
    '1': '进行中',
    '2': '已完成',
    'missed': '已漏巡'
  };

  // 优先使用数字状态码
  if (typeof status === 'number') {
    return numericStatusMap[status] || `状态${status}`;
  }

  // 字符串状态处理
  return stringStatusMap[status] || status;
};

// 获取当前巡更点索引
const getCurrentPointIndex = (plan: any) => {
  return plan.planCardList.findIndex((point: any) => point.status === 'current');
};

// 判断是否为下一个巡更点
const isNextPoint = (plan: any, index: number) => {
  const currentIndex = getCurrentPointIndex(plan);
  return currentIndex >= 0 && index === currentIndex + 1;
};

// 获取巡更点图标（支持数字状态码和字符串状态）
const getPointIcon = (status: string | number) => {
  // 数字状态码图标映射：0=待巡，1=正常，2=漏检
  const numericIconMap = {
    0: 'ant-design:clock-circle-filled',      // 待巡 - 时钟图标（黄色）
    1: 'ant-design:check-circle-filled',      // 正常 - 对勾图标（绿色）
    2: 'ant-design:close-circle-filled'       // 漏检 - 叉号图标（红色）
  };

  // 字符串状态图标映射（保持向后兼容）
  const stringIconMap = {
    'pending': 'ant-design:clock-circle-filled',    // 待巡更
    'checked': 'ant-design:check-circle-filled',    // 已巡更
    'current': 'ant-design:environment-filled',     // 当前位置
    'missed': 'ant-design:close-circle-filled'      // 漏巡
  };

  // 优先使用数字状态码
  if (typeof status === 'number') {
    return numericIconMap[status] || 'ant-design:clock-circle-filled';
  }

  // 字符串状态处理
  return stringIconMap[status] || 'ant-design:clock-circle-filled';
};

// 获取打卡点状态文本（支持数字状态码和字符串状态）
const getPointStatusText = (status: string | number) => {
  // 数字状态码文本映射：0=待巡，1=正常，2=漏检
  const numericStatusMap = {
    0: '待巡',    // 尚未到达该打卡点
    1: '正常',    // 已正常打卡
    2: '漏检'     // 错过了打卡时间
  };

  // 字符串状态文本映射（保持向后兼容）
  const stringStatusMap = {
    'pending': '待巡',
    'checked': '正常',
    'current': '当前',
    'missed': '漏检'
  };

  // 优先使用数字状态码
  if (typeof status === 'number') {
    return numericStatusMap[status] || `状态${status}`;
  }

  // 字符串状态处理
  return stringStatusMap[status] || status;
};



// 更新当前时间
const updateCurrentTime = () => {
  currentTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss');
};

// 全屏功能
const toggleFullscreen = () => {
  const element = document.documentElement;
  if (!document.fullscreenElement) {
    if (element.requestFullscreen) {
      element.requestFullscreen();
    }
    isFullscreen.value = true;
  }
};

const exitFullscreen = () => {
  if (document.exitFullscreen) {
    document.exitFullscreen();
  }
  isFullscreen.value = false;
};

// 监听全屏状态变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement;
};

// 初始化数据加载
const initializeData = async () => {
  try {
    isLoading.value = true;
    // 直接加载数据，在loadData中处理权限和数据状态
    await loadData();
  } catch (error) {
    console.error('初始化数据失败:', error);
    isEmpty.value = true;
    hasPermission.value = false;
  } finally {
    isLoading.value = false;
  }
};

// 加载数据
const loadData = async () => {
  try {
    const data = await getPrisonVisualizationData();
    // console.log('获取数据成功:', data);
    // 检查数据是否存在且有效
    if (data) {
      // 进一步检查是否有实际的人员数据
      const hasStaffData = data.lines.some(section =>
        section.cardList && section.cardList.length > 0
      );
      // console.log('数据是否有效:', hasStaffData);
      if (hasStaffData) {
        // 有有效数据
        prisonData.value = data;
        isEmpty.value = false;
        hasPermission.value = true;
      
      } else {
        // 没有人员数据，视为无权限/数据为空
        isEmpty.value = true;
        hasPermission.value = false;
       
      }
    } else {
      // 没有数据或数据结构无效，视为无权限/数据为空
      isEmpty.value = true;
      hasPermission.value = false;
      
    }
  } catch (error) {
    console.error('加载数据失败:', error);
    // 数据加载失败，视为无权限/数据为空
    isEmpty.value = true;
    hasPermission.value = false;
    
  }
};

// 组件挂载
onMounted(() => {
  updateCurrentTime();
  timeTimer = setInterval(updateCurrentTime, 1000);

  // 初始化数据加载
  initializeData();

  // 定时刷新数据
  dataTimer = setInterval(() => {
    loadData();
  }, 30000); // 30秒刷新一次数据

  // 监听全屏状态变化
  document.addEventListener('fullscreenchange', handleFullscreenChange);
});

// 组件卸载
onUnmounted(() => {
  if (timeTimer) clearInterval(timeTimer);
  if (dataTimer) clearInterval(dataTimer);
  document.removeEventListener('fullscreenchange', handleFullscreenChange);
});
</script>

<style lang="less" scoped>
.prison-visualization {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0f1419 100%);
  color: #ffffff;
  font-family: 'Microsoft YaHei', sans-serif;
  position: relative;
  overflow: hidden;

  &.fullscreen-mode {
    width: 1920px;
    height: 1080px;
    min-height: 1080px;
    max-height: 1080px;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    transform-origin: top left;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 防止整体页面滚动 */

    /* 根据屏幕尺寸自动缩放以适应不同分辨率 */
    @media (max-width: 1920px) {
      transform: scale(calc(100vw / 1920));
    }

    @media (max-height: 1080px) {
      transform: scale(calc(100vh / 1080));
    }

    @media (max-width: 1920px) and (max-height: 1080px) {
      transform: scale(min(calc(100vw / 1920), calc(100vh / 1080)));
    }
  }

  // 空状态样式
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    padding: 40px 20px;
    text-align: center;
    background: rgba(0, 255, 255, 0.02);
    border: 1px dashed rgba(0, 255, 255, 0.2);
    border-radius: 12px;
    margin: 20px;

    .empty-icon {
      font-size: 48px;
      color: rgba(0, 255, 255, 0.3);
      margin-bottom: 16px;

      .loading-icon {
        animation: spin 1s linear infinite;
        color: rgba(0, 255, 255, 0.6);
      }
    }

    .empty-text {
      font-size: 18px;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 8px;
      font-weight: 500;
      text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
    }

    .empty-description {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.5);
      line-height: 1.5;
    }
  }

  // 人员区域的空状态
  .staff-section .empty-state {
    min-height: 150px;
    margin: 10px 20px;
  }

  // 巡更计划区域的空状态
  .patrol-plans-section .empty-state {
    min-height: 300px;
    margin: 20px;

    .fullscreen-mode & {
      min-height: 400px;
    }
  }

  // 加载动画
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  /* 科技感背景效果 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(0, 150, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 60%, rgba(255, 0, 150, 0.05) 0%, transparent 50%);
    pointer-events: none;
  }
}

/* 头部区域 */
.visualization-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: linear-gradient(90deg, rgba(0, 255, 255, 0.1) 0%, rgba(0, 150, 255, 0.1) 100%);
  border-bottom: 2px solid rgba(0, 255, 255, 0.3);
  backdrop-filter: blur(10px);

  @media (min-width: 768px) {
    padding: 20px 40px;
  }

  .header-title {
    .title-glow {
      font-size: 24px;
      font-weight: bold;
      background: linear-gradient(45deg, #00ffff, #0096ff, #00ffff);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
      margin-bottom: 5px;

      @media (min-width: 768px) {
        font-size: 28px;
      }

      @media (min-width: 1200px) {
        font-size: 32px;
      }

      .fullscreen-mode & {
        font-size: 36px;
      }
    }

    .subtitle {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.7);
      text-align: center;

      @media (min-width: 768px) {
        font-size: 16px;
      }
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 10px;

    @media (min-width: 768px) {
      gap: 20px;
    }

    .header-time {
      font-size: 14px;
      color: #00ffff;
      font-weight: 500;
      text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);

      @media (min-width: 768px) {
        font-size: 16px;
      }

      @media (min-width: 1200px) {
        font-size: 18px;
      }
    }

    .fullscreen-btn, .exit-fullscreen-btn {
      background: linear-gradient(45deg, #0096ff, #00ffff);
      border: none;
      color: #ffffff;
      font-weight: 500;
      box-shadow: 0 0 15px rgba(0, 150, 255, 0.4);
      font-size: 12px;
      // padding: 6px 12px;

      @media (min-width: 768px) {
        font-size: 14px;
        // padding: 8px 16px;
      }

      &:hover {
        background: linear-gradient(45deg, #00ffff, #0096ff);
        box-shadow: 0 0 25px rgba(0, 255, 255, 0.6);
        transform: translateY(-2px);
      }
    }
  }
}

/* 人员信息区域 */
.staff-section {
  padding: 15px 10px;

  // 小屏幕优化 (480px+)
  @media (min-width: 480px) {
    padding: 18px 15px;
  }

  // 平板端 (768px+)
  @media (min-width: 768px) {
    padding: 20px 25px;
  }

  // 大平板端 (992px+)
  @media (min-width: 992px) {
    padding: 22px 30px;
  }

  // 桌面端 (1200px+)
  @media (min-width: 1200px) {
    padding: 25px 35px;
  }

  // 大桌面端 (1400px+)
  @media (min-width: 1400px) {
    padding: 30px 40px;
  }

  /* 全屏模式下的人员区域高度控制 */
  .fullscreen-mode & {
    padding: 20px 40px;
    // height: 280px; /* 固定高度 */
    // max-height: 280px;
    overflow: hidden;
    flex-shrink: 0; /* 防止被压缩 */
  }

  .section-title {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;

    @media (min-width: 768px) {
      margin-bottom: 25px;
    }

    @media (min-width: 1200px) {
      margin-bottom: 30px;
    }

    .title-line {
      flex: 1;
      height: 2px;
      background: linear-gradient(90deg, transparent, #00ffff, transparent);
    }

    span {
      margin: 0 15px;
      font-size: 18px;
      font-weight: bold;
      color: #00ffff;
      text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);

      @media (min-width: 768px) {
        margin: 0 20px;
        font-size: 20px;
      }

      @media (min-width: 1200px) {
        margin: 0 30px;
        font-size: 24px;
      }
    }
  }

  .sections-container {
    display: flex;
    flex-direction: column;
    gap: 20px;

    // 小屏幕优化 (480px+)
    @media (min-width: 480px) {
      gap: 22px;
    }

    // 平板端 (768px+)
    @media (min-width: 768px) {
      flex-direction: row;
      justify-content: space-between;
      gap: 20px;
    }

    // 大平板端 (992px+)
    @media (min-width: 992px) {
      gap: 25px;
    }

    // 桌面端 (1200px+)
    @media (min-width: 1200px) {
      gap: 30px;
    }

    // 大桌面端 (1400px+)
    @media (min-width: 1400px) {
      gap: 35px;
    }

    .section-group {
      flex: 1;
      // 添加分监区整体边框
      background: rgba(0, 255, 255, 0.03);
      border: 2px solid rgba(0, 255, 255, 0.2);
      border-radius: 12px;
      padding: 15px; // 非全屏模式下的基础内边距
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    

      // 多人员模式下增加最小高度以容纳两排（通过类名控制）
      &.section-has-many-staff {
        min-height: 160px; // 确保有足够空间显示两排

        @media (min-width: 480px) {
          min-height: 170px;
        }

        @media (min-width: 768px) {
          min-height: 180px;
        }

        @media (min-width: 992px) {
          min-height: 190px;
        }

        @media (min-width: 1200px) {
          min-height: 200px;
        }

        @media (min-width: 1400px) {
          min-height: 210px;
        }
      }

      // 添加发光效果
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.05), transparent);
        transition: left 0.8s ease;
      }

      &:hover {
        border-color: rgba(0, 255, 255, 0.4);
        box-shadow: 0 0 25px rgba(0, 255, 255, 0.2);
        transform: translateY(-2px);

        &::before {
          left: 100%;
        }
      }

      // 小屏幕优化 (480px+)
      @media (min-width: 480px) {
        padding: 16px;
        border-radius: 13px;
      }

      // 平板端 (768px+)
      @media (min-width: 768px) {
        padding: 18px;
        border-radius: 14px;
      }

      // 大平板端 (992px+)
      @media (min-width: 992px) {
        padding: 20px;
        border-radius: 15px;
      }

      // 桌面端 (1200px+)
      @media (min-width: 1200px) {
        padding: 22px;
        border-radius: 16px;
      }

      // 大桌面端 (1400px+)
      @media (min-width: 1400px) {
        padding: 25px;
        border-radius: 18px;
      }

      // 全屏模式下的特殊内边距
      .fullscreen-mode & {
        padding: 30px;
        border-radius: 20px;
      }

      .section-name {
        text-align: center;
        font-size: 14px;
        font-weight: bold;
        color: #00ffff;
        margin-bottom: 12px;
        text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
        position: relative;
        z-index: 2;

        // 添加分监区名称装饰
        &::before {
          content: '';
          position: absolute;
          bottom: -4px;
          left: 50%;
          transform: translateX(-50%);
          width: 50px;
          height: 2px;
          background: linear-gradient(90deg, transparent, #00ffff, transparent);
        }

        // 小屏幕优化 (480px+)
        @media (min-width: 480px) {
          font-size: 15px;
          margin-bottom: 13px;

          &::before {
            width: 55px;
          }
        }

        // 平板端 (768px+)
        @media (min-width: 768px) {
          font-size: 16px;
          margin-bottom: 14px;

          &::before {
            width: 65px;
          }
        }

        // 大平板端 (992px+)
        @media (min-width: 992px) {
          font-size: 17px;
          margin-bottom: 16px;

          &::before {
            width: 75px;
          }
        }

        // 桌面端 (1200px+)
        @media (min-width: 1200px) {
          font-size: 18px;
          margin-bottom: 18px;

          &::before {
            width: 85px;
          }
        }

        // 大桌面端 (1400px+)
        @media (min-width: 1400px) {
          font-size: 20px;
          margin-bottom: 20px;

          &::before {
            width: 100px;
          }
        }
      }

      .staff-cards {
        display: flex;
        justify-content: center;
        gap: 8px;
        flex-wrap: wrap;
        position: relative;
        z-index: 2;
        padding: 0 12px; // 添加左右内边距，防止紧贴边框
        align-content: flex-start; // 多行时从顶部开始对齐

        // 当人员超过3个时的处理（通过类名控制）
        &.has-many-staff {
          justify-content: space-evenly; // 均匀分布
          align-items: flex-start;
          flex-wrap: wrap; // 允许换行
          gap: 6px 8px; // 行间距6px，列间距8px

          // 确保两排显示的合理布局
          .staff-card {
            // 默认每行最多3个人员
            flex: 0 0 calc(33.333% - 10px);
            max-width: calc(33.333% - 10px);
            min-width: 80px;

            @media (min-width: 480px) {
              flex: 0 0 calc(33.333% - 10px);
              max-width: calc(33.333% - 10px);
              min-width: 85px;
            }

            @media (min-width: 768px) {
              flex: 0 0 calc(33.333% - 12px);
              max-width: calc(33.333% - 12px);
              min-width: 90px;
            }

            @media (min-width: 992px) {
              flex: 0 0 calc(33.333% - 15px);
              max-width: calc(33.333% - 15px);
              min-width: 95px;
            }

            @media (min-width: 1200px) {
              flex: 0 0 calc(33.333% - 18px);
              max-width: calc(33.333% - 18px);
              min-width: 100px;
            }
          }
        }

        // 正常情况下（3个或以下人员）的响应式优化
        &:not(.has-many-staff) {
          // 小屏幕优化 (480px+)
          @media (min-width: 480px) {
            gap: 10px;
            padding: 0 15px;
          }

          // 平板端 (768px+)
          @media (min-width: 768px) {
            gap: 10px;
            padding: 0 18px;
            flex-wrap: nowrap;
          }

          // 大平板端 (992px+)
          @media (min-width: 992px) {
            gap: 10px;
            padding: 0 20px;
          }

          // 桌面端 (1200px+)
          @media (min-width: 1200px) {
            gap: 10px;
            padding: 0 22px;
          }

          // 大桌面端 (1400px+)
          @media (min-width: 1400px) {
            gap: 10px;
            padding: 0 25px;
          }
        }

        // 多人员情况下的响应式优化
        &.has-many-staff {
          // 小屏幕优化 (480px+)
          @media (min-width: 480px) {
            gap: 8px 10px;
            padding: 0 15px;
          }

          // 平板端 (768px+)
          @media (min-width: 768px) {
            gap: 10px 12px;
            padding: 0 18px;
          }

          // 大平板端 (992px+)
          @media (min-width: 992px) {
            gap: 12px 15px;
            padding: 0 20px;
          }

          // 桌面端 (1200px+)
          @media (min-width: 1200px) {
            gap: 14px 18px;
            padding: 0 22px;
          }

          // 大桌面端 (1400px+)
          @media (min-width: 1400px) {
            gap: 16px 20px;
            padding: 0 25px;
          }
        }

        .staff-card {
          background: linear-gradient(135deg, rgba(0, 255, 255, 0.08), rgba(0, 150, 255, 0.08));
          border: 1px solid rgba(0, 255, 255, 0.25);
          border-radius: 6px;
          padding: 12px;
          text-align: center;
          transition: all 0.3s ease;
          backdrop-filter: blur(5px);
          position: relative;
          min-width: 100px;
          flex: 1;
          max-width: 140px;

          // 正常情况下（3个或以下人员）的尺寸
          .staff-cards:not(.has-many-staff) & {
            flex: 1;
            min-width: 100px;
            max-width: 140px;

            @media (min-width: 480px) {
              min-width: 110px;
              max-width: 150px;
            }

            @media (min-width: 768px) {
              min-width: 120px;
              max-width: 160px;
            }

            @media (min-width: 1200px) {
              min-width: 140px;
              max-width: 180px;
            }
          }

          // 多人员模式下的特殊样式
          .staff-cards.has-many-staff & {
            // 确保在两排显示时有合适的高度
            min-height: 60px;

            @media (min-width: 480px) {
              min-height: 65px;
            }

            @media (min-width: 768px) {
              min-height: 70px;
            }

            @media (min-width: 992px) {
              min-height: 75px;
            }

            @media (min-width: 1200px) {
              min-height: 80px;
            }

            // 调整内边距以适应较小的卡片
            padding: 8px;

            @media (min-width: 480px) {
              padding: 10px;
            }

            @media (min-width: 768px) {
              padding: 12px;
            }
          }

          // 小屏幕优化 (480px+)
          @media (min-width: 480px) {
            border-radius: 7px;
            padding: 13px;
            min-width: 110px;
            max-width: 150px;
          }

          // 平板端 (768px+)
          @media (min-width: 768px) {
            border-radius: 8px;
            padding: 14px;
            min-width: 120px;
            max-width: 160px;
          }

          // 大平板端 (992px+)
          @media (min-width: 992px) {
            border-radius: 9px;
            padding: 16px;
            min-width: 130px;
            max-width: 170px;
          }

          // 桌面端 (1200px+)
          @media (min-width: 1200px) {
            border-radius: 10px;
            padding: 18px;
            min-width: 140px;
            max-width: 180px;
          }

          // 大桌面端 (1400px+)
          @media (min-width: 1400px) {
            border-radius: 12px;
            padding: 20px;
            min-width: 160px;
            max-width: 200px;
          }

          &:hover {
            border-color: rgba(0, 255, 255, 0.5);
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.25);
            transform: translateY(-3px);
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.12), rgba(0, 150, 255, 0.12));
          }

          &.active {
            border-color: #00ff88;
            box-shadow: 0 0 25px rgba(0, 255, 136, 0.4);

            &::before {
              content: '';
              position: absolute;
              top: -2px;
              left: -2px;
              right: -2px;
              bottom: -2px;
              background: linear-gradient(45deg, #00ff88, #00ffff);
              border-radius: inherit;
              z-index: -1;
              animation: cardGlow 2s infinite;
              opacity: 0.6;
            }

            // 在线状态时头像发光
            .staff-avatar img {
              border-color: #00ff88;
              box-shadow: 0 0 12px rgba(0, 255, 136, 0.6);
              animation: avatarGlow 2s ease-in-out infinite;
            }
          }

          // 离线状态的人员卡片
          &.offline {
            opacity: 0.7;
          }



          .staff-avatar {
            position: relative;
            margin-bottom: 8px;

            // 小屏幕优化 (480px+)
            @media (min-width: 480px) {
              margin-bottom: 9px;
            }

            // 平板端 (768px+)
            @media (min-width: 768px) {
              margin-bottom: 10px;
            }

            // 大平板端 (992px+)
            @media (min-width: 992px) {
              margin-bottom: 12px;
            }

            // 桌面端 (1200px+)
            @media (min-width: 1200px) {
              margin-bottom: 14px;
            }

            // 大桌面端 (1400px+)
            @media (min-width: 1400px) {
              margin-bottom: 15px;
            }

            img {
              width: 50px;
              height: 50px;
              border-radius: 4px;
              object-fit: cover;
              border: 2px solid rgba(0, 255, 136, 0.5);
              transition: all 0.3s ease;

              // 小屏幕优化 (480px+)
              @media (min-width: 480px) {
                width: 55px;
                height: 55px;
                border-radius: 5px;
              }

              // 平板端 (768px+)
              @media (min-width: 768px) {
                width: 60px;
                height: 60px;
                border-radius: 6px;
              }

              // 大平板端 (992px+)
              @media (min-width: 992px) {
                width: 65px;
                height: 65px;
                border-radius: 6px;
              }

              // 桌面端 (1200px+)
              @media (min-width: 1200px) {
                width: 70px;
                height: 70px;
                border-radius: 7px;
              }

              // 大桌面端 (1400px+)
              @media (min-width: 1400px) {
                width: 80px;
                height: 80px;
                border-radius: 8px;
              }
            }

            .status-indicator {
              position: absolute;
              bottom: -5px;
              right: 10px;
              width: 12px;
              height: 12px;
              border-radius: 50%;
              border: 2px solid #ffffff;
              z-index: 10;

              // 小屏幕优化 (480px+)
              @media (min-width: 480px) {
                width: 13px;
                height: 13px;
              }

              // 平板端 (768px+)
              @media (min-width: 768px) {
                width: 14px;
                height: 14px;
              }

              // 大平板端 (992px+)
              @media (min-width: 992px) {
                width: 16px;
                height: 16px;
              }

              // 桌面端 (1200px+)
              @media (min-width: 1200px) {
                width: 18px;
                height: 18px;
              }

              // 大桌面端 (1400px+)
              @media (min-width: 1400px) {
                width: 20px;
                height: 20px;
              }

              &.on-duty {
                background: #00ff88;
                box-shadow:
                  0 0 12px rgba(0, 255, 136, 0.8),
                  0 0 0 0 rgba(0, 255, 136, 0.4);
                animation: statusPulse 2s infinite;

                // 添加发光环效果
                &::before {
                  content: '';
                  position: absolute;
                  top: -4px;
                  left: -4px;
                  right: -4px;
                  bottom: -4px;
                  background: linear-gradient(45deg, #00ff88, #00ffff);
                  border-radius: inherit;
                  z-index: -1;
                  animation: glowRing 2s infinite;
                  opacity: 0.6;
                }
              }

              &.offline {
                background: #666666;
                box-shadow: 0 0 4px rgba(102, 102, 102, 0.3);
                opacity: 0.7;
              }
            }
          }

          .staff-info {
            .staff-name {
              font-size: 12px;
              font-weight: bold;
              color: #ffffff;
              margin-bottom: 3px;

              // 小屏幕优化 (480px+)
              @media (min-width: 480px) {
                font-size: 13px;
                margin-bottom: 4px;
              }

              // 平板端 (768px+)
              @media (min-width: 768px) {
                font-size: 14px;
                margin-bottom: 4px;
              }

              // 大平板端 (992px+)
              @media (min-width: 992px) {
                font-size: 14px;
                margin-bottom: 5px;
              }

              // 桌面端 (1200px+)
              @media (min-width: 1200px) {
                font-size: 15px;
                margin-bottom: 5px;
              }

              // 大桌面端 (1400px+)
              @media (min-width: 1400px) {
                font-size: 16px;
                margin-bottom: 6px;
              }
            }

            .staff-card-id {
              font-size: 10px;
              color: #00ffff;
              margin-bottom: 3px;

              // 小屏幕优化 (480px+)
              @media (min-width: 480px) {
                font-size: 11px;
                margin-bottom: 3px;
              }

              // 平板端 (768px+)
              @media (min-width: 768px) {
                font-size: 12px;
                margin-bottom: 4px;
              }

              // 大平板端 (992px+)
              @media (min-width: 992px) {
                font-size: 12px;
                margin-bottom: 4px;
              }

              // 桌面端 (1200px+)
              @media (min-width: 1200px) {
                font-size: 13px;
                margin-bottom: 5px;
              }

              // 大桌面端 (1400px+)
              @media (min-width: 1400px) {
                font-size: 14px;
                margin-bottom: 5px;
              }
            }

            .staff-status {
              font-size: 9px;
              color: rgba(255, 255, 255, 0.7);
              font-weight: 500;
              padding: 2px 6px;
              border-radius: 8px;
              background: rgba(255, 255, 255, 0.05);
              border: 1px solid rgba(255, 255, 255, 0.1);
              display: inline-block;
              transition: all 0.3s ease;

              // 小屏幕优化 (480px+)
              @media (min-width: 480px) {
                font-size: 10px;
                padding: 2px 7px;
              }

              // 平板端 (768px+)
              @media (min-width: 768px) {
                font-size: 11px;
                padding: 3px 8px;
                border-radius: 10px;
              }

              // 大平板端 (992px+)
              @media (min-width: 992px) {
                font-size: 11px;
                padding: 3px 8px;
              }

              // 桌面端 (1200px+)
              @media (min-width: 1200px) {
                font-size: 12px;
                padding: 4px 10px;
                border-radius: 12px;
              }

              // 大桌面端 (1400px+)
              @media (min-width: 1400px) {
                font-size: 13px;
                padding: 4px 12px;
              }

              // 不同状态的颜色 - 与 PatrolDashboard.vue 保持一致
              &.online {
                color: #00ff88;
                background: rgba(0, 255, 136, 0.1);
                border-color: rgba(0, 255, 136, 0.3);
                box-shadow: 0 0 8px rgba(0, 255, 136, 0.2);
                animation: statusTextGlow 2s ease-in-out infinite;
              }

              &.on-duty {
                color: #00ff88;
                background: rgba(0, 255, 136, 0.1);
                border-color: rgba(0, 255, 136, 0.3);
                box-shadow: 0 0 8px rgba(0, 255, 136, 0.2);
                animation: statusTextGlow 2s ease-in-out infinite;
              }

              &.offline {
                color: rgba(255, 255, 255, 0.5);
                background: rgba(102, 102, 102, 0.1);
                border-color: rgba(102, 102, 102, 0.3);
              }
            }
          }
        }
      }
    }
  }
}

/* 巡更计划区域 */
.patrol-plans-section {
  padding: 0 4vw 2vh;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;

  @media (max-width: 767px) {
    padding: 0 15px 20px;
  }

  /* 全屏模式下的巡更计划区域 */
  .fullscreen-mode & {
    padding: 30px 40px 20px; /* 增加顶部间距30px */
    height: calc(1080px - 430px); /* 调整高度，减去增加的顶部间距 */
    max-height: calc(1080px - 430px);
    flex: 1;
    overflow: hidden;
    margin-top: 0; /* 确保没有额外的外边距 */
  }

  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 1.5vh;
    position: relative;

    @media (max-width: 767px) {
      margin-bottom: 20px;
    }

    .title-line {
      flex: 1;
      height: 2px;
      background: linear-gradient(90deg, transparent, #00ffff, transparent);
    }

    span {
      margin: 0 20px;
      font-size: clamp(16px, 2vw, 24px);
      font-weight: bold;
      color: #00ffff;
      text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);

      @media (max-width: 767px) {
        margin: 0 15px;
        font-size: 18px;
      }
    }
  }

  .plans-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* 允许flex子项收缩 */
    /* 普通模式下自适应高度 */
    max-height: calc(100vh - 300px); /* 普通模式下限制最大高度 */
    overflow: hidden;
  }

  /* 全屏模式下的高度限制 */
  .fullscreen-mode .plans-container {
    height: calc(1080px - 430px); /* 调整高度，考虑增加的间距 */
    max-height: calc(1080px - 430px);
    overflow: hidden; /* 全屏时隐藏外层滚动 */
  }

  .plans-scroll {
    display: flex;
    flex-direction: column;
    gap: clamp(8px, 1vh, 12px); /* 普通模式下的间距 */
    padding-bottom: clamp(20px, 3vh, 40px); /* 普通模式下的底部内边距 */
    flex: 1; /* 占满剩余空间 */
    overflow-y: auto; /* 普通模式下也启用滚动 */
    overflow-x: hidden;
    padding-right: 8px; /* 为滚动条留出空间 */

    /* 普通模式下的滚动条样式 */
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: linear-gradient(45deg, rgba(0, 255, 255, 0.4), rgba(0, 150, 255, 0.6));
      border-radius: 4px;
      border: 1px solid rgba(0, 255, 255, 0.2);

      &:hover {
        background: linear-gradient(45deg, rgba(0, 255, 255, 0.6), rgba(0, 150, 255, 0.8));
      }
    }
  }

  /* 只有全屏模式下才添加滚动区域限制 */
  .fullscreen-mode .plans-scroll {
    height: calc(1080px - 480px); /* 调整高度，考虑增加的间距 */
    max-height: calc(1080px - 480px);
    overflow-y: auto; /* 全屏时启用滚动 */
    overflow-x: hidden; /* 隐藏水平滚动 */
    padding-right: 12px; /* 全屏时为滚动条留出更多空间 */
    padding-bottom: 20px; /* 全屏时的底部空间 */
    gap: 8px; /* 全屏时固定间距 */
    padding-top: 0; /* 减少顶部内边距，因为外层已经有间距 */
    flex: 1; /* 占满剩余空间 */

    /* 全屏时的滚动条样式 */
    &::-webkit-scrollbar {
      width: 12px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.3);
      border-radius: 6px;
      border: 1px solid rgba(0, 255, 255, 0.2);
    }

    &::-webkit-scrollbar-thumb {
      background: linear-gradient(45deg, rgba(0, 255, 255, 0.6), rgba(0, 150, 255, 0.8));
      border-radius: 6px;
      border: 1px solid rgba(0, 255, 255, 0.3);
      box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);

      &:hover {
        background: linear-gradient(45deg, rgba(0, 255, 255, 0.8), rgba(0, 150, 255, 1));
        box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
      }
    }

    &::-webkit-scrollbar-corner {
      background: rgba(0, 0, 0, 0.3);
    }
  }

  .plan-item {
    padding: clamp(12px, 1.5vw, 18px); /* 普通模式下的内边距 */
    background: rgba(0, 0, 0, 0.4);
    border: 2px solid rgba(0, 255, 255, 0.3);
    border-radius: 12px; /* 普通模式下的圆角 */
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    flex-shrink: 0; /* 防止列表项被压缩 */
    margin-bottom: clamp(12px, 1.5vh, 20px); /* 普通模式下的底部间距 */

    @media (max-width: 767px) {
      border-radius: 8px;
      margin-bottom: 15px;
      padding: 15px;
    }

    // 字符串状态样式（保持向后兼容）
    &.in-progress {
      border-color: #1890ff;
      box-shadow: 0 0 15px rgba(24, 144, 255, 0.3);

      .plan-status-badge {
        background: linear-gradient(45deg, #1890ff, #40a9ff);
      }
    }

    &.pending {
      border-color: #faad14;

      .plan-status-badge {
        background: linear-gradient(45deg, #faad14, #ffc53d);
      }
    }

    &.completed {
      border-color: #52c41a;

      .plan-status-badge {
        background: linear-gradient(45deg, #52c41a, #73d13d);
      }
    }

    &.missed {
      border-color: #ff4d4f;

      .plan-status-badge {
        background: linear-gradient(45deg, #ff4d4f, #ff7875);
      }
    }

    // 数字状态码样式
    &.status-0 {
      border-color: #faad14; // 待巡更 - 橙色

      .plan-status-badge {
        background: linear-gradient(45deg, #faad14, #ffc53d);
      }
    }

    &.status-1 {
      border-color: #1890ff; // 进行中 - 蓝色
      box-shadow: 0 0 15px rgba(24, 144, 255, 0.3);

      .plan-status-badge {
        background: linear-gradient(45deg, #1890ff, #40a9ff);
      }
    }

    &.status-2 {
      border-color: #52c41a; // 已完成 - 绿色

      .plan-status-badge {
        background: linear-gradient(45deg, #52c41a, #73d13d);
      }
    }

    &:hover {
      transform: translateX(5px); /* 减小移动距离 */
      box-shadow: 0 3px 15px rgba(0, 255, 255, 0.2);
    }
  }

  /* 计划摘要 - 一行显示 */
  .plan-summary {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: clamp(10px, 1.2vh, 15px); /* 普通模式下的间距 */
    padding: clamp(10px, 1.2vh, 15px); /* 普通模式下的内边距 */
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px; /* 普通模式下的圆角 */
    border: 1px solid rgba(0, 255, 255, 0.2);
    position: relative;
    overflow: hidden;

    @media (max-width: 767px) {
      flex-wrap: wrap;
      gap: 8px;
    }

    /* 添加实时进度条 */
    &::before {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      height: 3px; /* 普通模式下的进度条高度 */
      background: linear-gradient(90deg, #1890ff, #40a9ff);
      animation: progressBar 10s linear infinite;
      box-shadow: 0 0 6px rgba(24, 144, 255, 0.6);
    }

    .summary-item {
      display: flex;
      align-items: center;
      gap: 6px;

      @media (max-width: 767px) {
        gap: 4px;
      }

      .label {
        font-size: clamp(12px, 1.2vw, 14px); /* 普通模式下的字体 */
        color: rgba(0, 255, 255, 0.8);
        font-weight: bold;
        white-space: nowrap;

        @media (max-width: 767px) {
          font-size: 11px;
        }
      }

      .value {
        font-size: clamp(12px, 1.2vw, 14px); /* 普通模式下的字体 */
        color: #ffffff;
        font-weight: normal;
        white-space: nowrap;

        @media (max-width: 767px) {
          font-size: 11px;
        }
      }

      .staff-info {
        display: flex;
        align-items: center;
        gap: 6px;

        .staff-avatar-tiny {
          width: clamp(18px, 1.8vw, 24px); /* 增大头像 */
          height: clamp(18px, 1.8vw, 24px); /* 增大头像 */
          border-radius: 4px;
          border: 1px solid rgba(0, 255, 255, 0.5);
          object-fit: cover;
          flex-shrink: 0;

          @media (max-width: 767px) {
            width: 16px;
            height: 16px;
          }
        }
      }
    }

    .plan-status-badge {
      padding: clamp(5px, 0.8vw, 8px) clamp(12px, 1.5vw, 16px); /* 增大内边距 */
      border-radius: 12px;
      font-size: clamp(11px, 1.1vw, 13px); /* 增大字体 */
      font-weight: bold;
      color: #ffffff;
      text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
      white-space: nowrap;
      flex-shrink: 0;

      @media (max-width: 767px) {
        padding: 4px 8px;
        font-size: 10px;
      }
    }
  }

  .patrol-points {
    display: flex;
    align-items: center;
    justify-content: space-between; /* 均匀分布 */
    margin-bottom: clamp(8px, 1vh, 12px);
    padding: clamp(8px, 1vh, 12px) 0;
    position: relative;

    .patrol-point {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center; /* 添加垂直居中 */
      position: relative;
      flex: 1; /* 平均分配宽度 */
      max-width: none; /* 移除最大宽度限制 */
      min-width: 0; /* 允许收缩 */
      min-height: 60px; /* 设置最小高度确保有足够空间 */

      /* 当前位置特殊样式 - 增强视觉效果 */
      &.is-current {
        /* 整个点位容器发光 */
        background: rgba(24, 144, 255, 0.1);
        border-radius: 8px;
        padding: 4px;
        margin: -4px;
        box-shadow: 0 0 15px rgba(24, 144, 255, 0.3);
        animation: currentContainerGlow 2s ease-in-out infinite;

        .point-icon {
          transform: scale(1.15);
          animation: currentPointPulse 2s ease-in-out infinite;
          z-index: 15; /* 确保当前位置图标在连接线之上 */
          position: relative;
          background: rgba(24, 144, 255, 0.2);
          border: 2px solid #1890ff;

          /* 添加实时巡更指示器 */
          &::before {
            content: '';
            position: absolute;
            top: -8px;
            right: -8px;
            width: 12px;
            height: 12px;
            background: #1890ff;
            border-radius: 50%;
            animation: patrolIndicator 1s ease-in-out infinite;
            box-shadow: 0 0 8px rgba(24, 144, 255, 0.8);
          }

          /* 添加巡更进度环 */
          &::after {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            bottom: -4px;
            border: 2px solid transparent;
            border-top: 2px solid #1890ff;
            border-radius: 50%;
            animation: progressRing 3s linear infinite;
          }
        }

        .point-name {
          color: #1890ff;
          font-weight: bold;
          text-shadow: 0 0 3px rgba(24, 144, 255, 0.8);
          animation: textGlow 2s ease-in-out infinite;
          position: relative;

          /* 添加"正在巡更"提示 */
          &::after {
            content: '正在巡更...';
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 8px;
            color: #1890ff;
            white-space: nowrap;
            animation: patrolText 2s ease-in-out infinite;
          }
        }

        .point-time {
          animation: textGlow 2s ease-in-out infinite;
        }

        /* 隐藏当前位置的连接线，避免重复显示 */
        .point-connector {
          opacity: 0.3; /* 降低透明度，减少视觉干扰 */
        }
      }

      /* 下一个目标特殊样式 - 增强视觉效果 */
      &.is-next {
        /* 整个点位容器发光 */
        background: rgba(250, 173, 20, 0.1);
        border-radius: 8px;
        padding: 4px;
        margin: -4px;
        box-shadow: 0 0 12px rgba(250, 173, 20, 0.3);
        animation: nextContainerBlink 1.5s ease-in-out infinite;

        .point-icon {
          animation: nextPointBlink 1.5s ease-in-out infinite;
          position: relative;
          background: rgba(250, 173, 20, 0.2);
          border: 2px solid #faad14;

          /* 添加等待指示器 */
          &::before {
            content: '';
            position: absolute;
            top: -6px;
            left: -6px;
            right: -6px;
            bottom: -6px;
            border: 2px dashed #faad14;
            border-radius: 50%;
            animation: waitingRing 4s linear infinite;
            opacity: 0.7;
          }

          /* 添加倒计时效果 */
          &::after {
            content: '';
            position: absolute;
            top: -10px;
            right: -10px;
            width: 8px;
            height: 8px;
            background: #faad14;
            border-radius: 50%;
            animation: countdown 1s ease-in-out infinite;
            box-shadow: 0 0 6px rgba(250, 173, 20, 0.8);
          }
        }

        .point-name {
          color: #faad14;
          font-weight: bold;
          text-shadow: 0 0 3px rgba(250, 173, 20, 0.8);
          animation: nextTextBlink 1.5s ease-in-out infinite;
          position: relative;

          /* 添加"即将到达"提示 */
          &::after {
            content: '即将到达';
            position: absolute;
            top: -18px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 8px;
            color: #faad14;
            white-space: nowrap;
            animation: nextAlert 1.5s ease-in-out infinite;
          }
        }

        .point-time {
          animation: nextTextBlink 1.5s ease-in-out infinite;
        }
      }

      .point-icon {
        width: clamp(24px, 2.5vw, 32px); /* 普通模式下的尺寸 */
        height: clamp(24px, 2.5vw, 32px);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: clamp(5px, 0.8vh, 8px); /* 普通模式下的间距 */
        transition: all 0.3s ease, transform 0.2s ease, box-shadow 0.2s ease;
        position: relative;
        z-index: 2;
        border: 2px solid transparent;

        @media (max-width: 767px) {
          width: 20px;
          height: 20px;
          margin-bottom: 4px;
        }

        /* 已巡更状态 - 绿色 */
        &.icon-checked {
          background: linear-gradient(45deg, #52c41a, #73d13d);
          box-shadow: 0 0 10px rgba(82, 196, 26, 0.6);
          border: 1px solid #ffffff;

          :deep(.anticon) {
            color: #ffffff !important;
            font-size: clamp(10px, 1.2vw, 14px) !important;

            @media (max-width: 767px) {
              font-size: 8px !important;
            }
          }
        }

        /* 当前位置状态 - 蓝色 */
        &.icon-current {
          background: linear-gradient(45deg, #1890ff, #40a9ff);
          box-shadow: 0 0 15px rgba(24, 144, 255, 1);
          border: 2px solid #ffffff;

          :deep(.anticon) {
            color: #ffffff !important;
            font-size: clamp(12px, 1.4vw, 16px) !important;

            @media (max-width: 767px) {
              font-size: 10px !important;
            }
          }
        }

        /* 待巡更状态 - 黄色 */
        &.icon-pending {
          background: linear-gradient(45deg, #faad14, #ffc53d);
          border: 1px solid #ffffff;
          box-shadow: 0 0 10px rgba(250, 173, 20, 0.6);

          :deep(.anticon) {
            color: #ffffff !important;
            font-size: clamp(9px, 1.1vw, 12px) !important; /* 增大字体 */

            @media (max-width: 767px) {
              font-size: 7px !important;
            }
          }
        }

        /* 漏巡状态 - 红色 */
        &.icon-missed {
          background: linear-gradient(45deg, #ff4d4f, #ff7875);
          box-shadow: 0 0 10px rgba(255, 77, 79, 0.6);
          border: 1px solid #ffffff;

          :deep(.anticon) {
            color: #ffffff !important;
            font-size: clamp(10px, 1.2vw, 14px) !important; /* 增大字体 */

            @media (max-width: 767px) {
              font-size: 8px !important;
            }
          }
        }

        /* 数字状态码样式 */
        /* 状态0 - 待巡 (黄色) */
        &.icon-0 {
          background: linear-gradient(45deg, #faad14, #ffc53d);
          border: 1px solid #ffffff;
          box-shadow: 0 0 10px rgba(250, 173, 20, 0.6);

          :deep(.anticon) {
            color: #ffffff !important;
            font-size: clamp(9px, 1.1vw, 12px) !important;

            @media (max-width: 767px) {
              font-size: 7px !important;
            }
          }
        }

        /* 状态1 - 正常 (绿色) */
        &.icon-1 {
          background: linear-gradient(45deg, #52c41a, #73d13d);
          box-shadow: 0 0 10px rgba(82, 196, 26, 0.6);
          border: 1px solid #ffffff;

          :deep(.anticon) {
            color: #ffffff !important;
            font-size: clamp(10px, 1.2vw, 14px) !important;

            @media (max-width: 767px) {
              font-size: 8px !important;
            }
          }
        }

        /* 状态2 - 漏检 (红色) */
        &.icon-2 {
          background: linear-gradient(45deg, #ff4d4f, #ff7875);
          box-shadow: 0 0 10px rgba(255, 77, 79, 0.6);
          border: 1px solid #ffffff;

          :deep(.anticon) {
            color: #ffffff !important;
            font-size: clamp(10px, 1.2vw, 14px) !important;

            @media (max-width: 767px) {
              font-size: 8px !important;
            }
          }
        }
      }

      .point-info {
        text-align: center;
        min-height: clamp(25px, 3vh, 35px); /* 增加高度 */
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        width: 100%; /* 占满父容器宽度 */

        .point-name {
          font-size: clamp(10px, 1vw, 12px); /* 普通模式下的字体 */
          color: #ffffff;
          margin-bottom: 2px; /* 普通模式下的间距 */
          white-space: nowrap;
          width: 100%; /* 占满宽度 */
          overflow: hidden;
          text-overflow: ellipsis;
          line-height: 1.2;
          transition: all 0.3s ease;

          @media (max-width: 767px) {
            font-size: 9px;
            margin-bottom: 1px;
          }
        }

        .point-time {
          font-size: clamp(9px, 0.9vw, 10px); /* 普通模式下的字体 */
          color: rgba(0, 255, 255, 0.8);
          font-family: 'Courier New', monospace;
          font-weight: bold;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          transition: all 0.3s ease;

          @media (max-width: 767px) {
            font-size: 8px;
          }
        }
      }

      /* 连接线 - 箭头刚好连接到下一个打卡点 */
      .point-connector {
        position: absolute;
        top: clamp(12px, 1.25vw, 16px); /* 基于图标中心位置 */
        left: 50%; /* 从当前点中心开始 */
        width: calc(100% - clamp(30px, 3vw, 40px)); /* 缩短连接线，为箭头留出空间 */
        height: 3px; /* 连接线高度 */
        z-index: 5; /* 提高z-index */
        transform: translateX(clamp(12px, 1.25vw, 16px)); /* 从图标右边缘开始 */
        pointer-events: none; /* 避免阻挡点击事件 */

        @media (max-width: 767px) {
          top: 10px;
          height: 2px;
          transform: translateX(10px);
          width: calc(100% - 20px);
        }

        .connector-line {
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, #52c41a, #73d13d); /* 默认绿色渐变 */
          border-radius: 2px;
          position: relative;
          box-shadow: 0 0 4px rgba(82, 196, 26, 0.4);

          /* 箭头指示器 */
          &::after {
            content: '';
            position: absolute;
            right: -6px; /* 箭头位置 */
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 6px solid #73d13d; /* 箭头颜色 */
            border-top: 4px solid transparent;
            border-bottom: 4px solid transparent;
            filter: drop-shadow(0 0 2px rgba(82, 196, 26, 0.6));

            @media (max-width: 767px) {
              right: -4px;
              border-left: 4px solid #73d13d;
              border-top: 3px solid transparent;
              border-bottom: 3px solid transparent;
            }
          }
        }

        /* 进行中状态的连接线 */
        .status-in-progress & {
          .connector-line {
            background: linear-gradient(90deg, #1890ff, #40a9ff);
            animation: flowAnimation 2s linear infinite;
            box-shadow: 0 0 6px rgba(24, 144, 255, 0.6);

            &::after {
              border-left-color: #40a9ff;
            }
          }
        }

        /* 待巡更状态的连接线 */
        .status-pending & {
          .connector-line {
            background: linear-gradient(90deg, #faad14, #ffc53d);
            box-shadow: 0 0 4px rgba(250, 173, 20, 0.4);

            &::after {
              border-left-color: #ffc53d;
            }
          }
        }

        /* 漏巡状态的连接线 */
        .status-missed & {
          .connector-line {
            background: linear-gradient(90deg, #ff4d4f, #ff7875);
            box-shadow: 0 0 4px rgba(255, 77, 79, 0.4);

            &::after {
              border-left-color: #ff7875;
            }
          }
        }
      }
    }
  }

  /* 全屏模式下的样式调整 */
  .fullscreen-mode & {
    .plan-item {
      padding: 12px; /* 全屏时固定内边距 */
      border-radius: 8px; /* 全屏时减少圆角 */
      margin-bottom: 6px; /* 全屏时固定底部间距 */
      min-height: auto; /* 允许高度自适应 */
    }

    .plan-summary {
      margin-bottom: 8px; /* 全屏时减少底部间距 */
      padding: 10px; /* 全屏时固定内边距 */
      border-radius: 6px; /* 全屏时减少圆角 */

      &::before {
        height: 2px; /* 全屏时进度条更细 */
      }

      .summary-item {
        .label {
          font-size: 12px; /* 全屏时固定字体大小 */
        }

        .value {
          font-size: 12px; /* 全屏时固定字体大小 */
        }

        .staff-avatar-tiny {
          width: 20px; /* 全屏时压缩头像 */
          height: 20px;
        }
      }

      .plan-status-badge {
        padding: 4px 10px; /* 全屏时压缩徽章 */
        font-size: 11px; /* 全屏时减小字体 */
      }
    }

    .patrol-points {
      margin-bottom: 6px; /* 全屏时减少间距 */
      padding: 6px 0; /* 全屏时减少内边距 */
      min-height: 50px; /* 全屏时设置最小高度 */
      display: flex;
      align-items: center; /* 确保整个巡更点行垂直居中 */
    }

    .patrol-point {
      min-height: 50px; /* 全屏时设置最小高度 */
      justify-content: center; /* 确保垂直居中 */
    }

    .point-icon {
      width: 24px; /* 全屏时固定图标尺寸 */
      height: 24px;
      margin-bottom: 3px; /* 全屏时适当间距 */
      flex-shrink: 0; /* 防止图标被压缩 */

      :deep(.anticon) {
        font-size: 12px !important; /* 全屏时固定图标字体 */
      }
    }

    .point-info {
      min-height: 18px; /* 全屏时减少最小高度 */
      display: flex;
      flex-direction: column;
      justify-content: center; /* 文字信息垂直居中 */
      align-items: center; /* 文字信息水平居中 */

      .point-name {
        font-size: 10px; /* 全屏时固定字体 */
        margin-bottom: 1px; /* 全屏时减少间距 */
        line-height: 1.2; /* 调整行高 */
      }

      .point-time {
        font-size: 9px; /* 全屏时固定字体 */
        line-height: 1.1; /* 调整行高 */
      }
    }

    .point-connector {
      top: 12px; /* 全屏时调整连接线位置 */
      transform: translateX(12px); /* 从图标右边缘开始 */
      height: 2px; /* 全屏时减少连接线高度 */

      .connector-line::after {
        border-left: 4px solid; /* 全屏时减小箭头 */
        border-top: 2px solid transparent;
        border-bottom: 2px solid transparent;
        right: -4px;
      }
    }
  }
}

/* 动画定义 */
@keyframes progressBar {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

@keyframes currentPointPulse {
  0%, 100% {
    transform: scale(1.15);
    box-shadow:
      0 0 12px rgba(24, 144, 255, 0.8),
      0 0 0 0 rgba(24, 144, 255, 0.4);
  }
  50% {
    transform: scale(1.25);
    box-shadow:
      0 0 20px rgba(24, 144, 255, 1),
      0 0 0 8px rgba(24, 144, 255, 0.2);
  }
}

@keyframes patrolIndicator {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.3);
    opacity: 0.7;
  }
}

@keyframes progressRing {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes textGlow {
  0%, 100% {
    text-shadow: 0 0 3px rgba(24, 144, 255, 0.8);
  }
  50% {
    text-shadow: 0 0 8px rgba(24, 144, 255, 1), 0 0 12px rgba(24, 144, 255, 0.6);
  }
}

@keyframes patrolText {
  0%, 100% {
    opacity: 0.7;
    transform: translateX(-50%) translateY(0);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) translateY(-2px);
  }
}

@keyframes nextPointBlink {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

@keyframes waitingRing {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(-360deg);
  }
}

@keyframes countdown {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.5;
  }
}

@keyframes nextTextBlink {
  0%, 100% {
    opacity: 1;
    text-shadow: 0 0 3px rgba(250, 173, 20, 0.8);
  }
  50% {
    opacity: 0.8;
    text-shadow: 0 0 6px rgba(250, 173, 20, 1), 0 0 10px rgba(250, 173, 20, 0.6);
  }
}

@keyframes nextAlert {
  0%, 100% {
    opacity: 0.6;
    transform: translateX(-50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) scale(1.1);
  }
}

@keyframes flowAnimation {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 100% 50%;
  }
}

@keyframes statusPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow:
      0 0 12px rgba(0, 255, 136, 0.8),
      0 0 0 0 rgba(0, 255, 136, 0.4);
  }
  50% {
    transform: scale(1.1);
    box-shadow:
      0 0 20px rgba(0, 255, 136, 1),
      0 0 0 8px rgba(0, 255, 136, 0.2);
  }
}

@keyframes glowRing {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.2);
  }
}

@keyframes cardGlow {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes statusTextGlow {
  0%, 100% {
    color: #00ff88;
    text-shadow: 0 0 4px rgba(0, 255, 136, 0.6);
    box-shadow: 0 0 8px rgba(0, 255, 136, 0.2);
  }
  50% {
    color: #00ffaa;
    text-shadow: 0 0 8px rgba(0, 255, 136, 0.8);
    box-shadow: 0 0 12px rgba(0, 255, 136, 0.4);
  }
}

@keyframes currentContainerGlow {
  0%, 100% {
    background: rgba(24, 144, 255, 0.1);
    box-shadow: 0 0 15px rgba(24, 144, 255, 0.3);
  }
  50% {
    background: rgba(24, 144, 255, 0.15);
    box-shadow: 0 0 25px rgba(24, 144, 255, 0.5);
  }
}

@keyframes nextContainerBlink {
  0%, 100% {
    background: rgba(250, 173, 20, 0.1);
    box-shadow: 0 0 12px rgba(250, 173, 20, 0.3);
  }
  50% {
    background: rgba(250, 173, 20, 0.15);
    box-shadow: 0 0 20px rgba(250, 173, 20, 0.5);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes currentPulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(0, 150, 255, 0.8);
  }
  50% {
    box-shadow: 0 0 30px rgba(0, 150, 255, 1);
  }
}

@keyframes flowAnimation {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 100% 50%;
  }
}
</style>
