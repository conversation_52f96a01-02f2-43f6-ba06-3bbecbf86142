# FLV 视频播放支持安装指南

## 概述

本项目已经优化了视频监控播放部分，添加了对 FLV 格式视频的支持。为了获得完整的 FLV 播放功能，需要安装 `flv.js` 依赖。

## 安装步骤

### 1. 安装 flv.js 依赖

在项目根目录执行以下命令之一：

```bash
# 使用 npm
npm install flv.js

# 使用 yarn  
yarn add flv.js

# 使用 pnpm
pnpm add flv.js
```

### 2. 安装类型定义（可选）

如果使用 TypeScript，建议安装类型定义：

```bash
# 使用 npm
npm install --save-dev @types/flv.js

# 使用 yarn
yarn add --dev @types/flv.js

# 使用 pnpm  
pnpm add --save-dev @types/flv.js
```

### 3. 验证安装

安装完成后，重新启动开发服务器：

```bash
npm run dev
```

在浏览器控制台中应该能看到：
```
flv.js 加载成功
```

## 功能特性

### 支持的视频格式

- ✅ **FLV** - Flash Video 格式（新增支持）
- ✅ **HLS** - HTTP Live Streaming (.m3u8)  
- ✅ **MP4** - MPEG-4 视频格式
- ✅ **RTMP** - Real-Time Messaging Protocol
- ✅ **RTSP** - Real-Time Streaming Protocol

### 自动格式检测

组件会根据视频 URL 自动检测格式：

```javascript
// FLV 格式示例
'http://example.com/live/stream.flv'
'https://example.com/video.flv?token=abc'

// HLS 格式示例  
'http://example.com/live/playlist.m3u8'

// MP4 格式示例
'http://example.com/video.mp4'
```

### 播放器优化

1. **动态加载** - flv.js 按需加载，不影响初始包大小
2. **错误恢复** - 自动重连和错误恢复机制
3. **资源管理** - 组件卸载时自动清理资源
4. **性能优化** - 针对直播流的缓冲策略

## 使用方法

### 基本用法

```vue
<template>
  <VideoMonitorPlayerModal
    :plan-id="planId"
    :video-info="videoInfo"
    :auto-start="false"
    :show-controls="true"
    @error="onVideoError"
  />
</template>

<script setup>
const videoInfo = {
  id: 'video1',
  name: '监控摄像头1',
  videoUrl: 'http://example.com/stream.flv', // FLV 格式
  streamId: 'stream_001',
  websocketUrl: '/websocket/video/stream_001',
  hlsUrl: '/hls/stream_001/index.m3u8',
  streamType: 'preview'
};

function onVideoError(error) {
  console.error('视频播放错误:', error);
}
</script>
```

### 测试组件

项目中包含了一个测试组件 `FlvVideoTest.vue`，可以用来测试不同格式的视频播放：

```vue
<template>
  <FlvVideoTest />
</template>

<script setup>
import FlvVideoTest from '@/views/plan/components/FlvVideoTest.vue';
</script>
```

## 兼容性说明

### 浏览器支持

- ✅ Chrome 45+
- ✅ Firefox 42+  
- ✅ Safari 10+
- ✅ Edge 13+
- ⚠️ IE 11（部分支持）

### 移动端支持

- ✅ iOS Safari 10+
- ✅ Android Chrome 45+
- ⚠️ 某些移动浏览器可能需要用户手动触发播放

## 故障排除

### 常见问题

1. **flv.js 未安装错误**
   ```
   Error: FLV播放器未安装，请安装 flv.js 依赖
   ```
   **解决方案**: 执行 `npm install flv.js`

2. **视频无法播放**
   - 检查视频 URL 是否可访问
   - 确认服务器支持 CORS
   - 查看浏览器控制台错误信息

3. **播放卡顿**
   - 检查网络带宽
   - 考虑降低视频质量
   - 调整缓冲配置

### 调试信息

在浏览器控制台中查看详细日志：

```javascript
// 启用调试模式
localStorage.setItem('video-debug', 'true');

// 查看支持的格式
console.log('FLV 支持:', flvjs?.isSupported());
console.log('HLS 支持:', Hls.isSupported());
```

## 配置选项

### FLV 播放器配置

可以通过修改 `VideoMonitorPlayerModal.vue` 中的配置来调整播放器行为：

```javascript
// FLV 播放器配置
const flvConfig = {
  type: 'flv',
  url: videoUrl,
  isLive: true,        // 是否为直播流
  hasAudio: true,      // 是否包含音频
  hasVideo: true,      // 是否包含视频
  cors: true,          // 启用 CORS
  withCredentials: false
};

// FLV 播放器选项
const flvOptions = {
  enableWorker: false,              // 禁用 Worker
  enableStashBuffer: true,          // 启用缓存缓冲
  stashInitialSize: 128,           // 初始缓存大小 (KB)
  autoCleanupSourceBuffer: true,    // 自动清理源缓冲
  lazyLoad: true,                  // 延迟加载
  lazyLoadMaxDuration: 180,        // 最大延迟加载时长 (秒)
  fixAudioTimestampGap: true       // 修复音频时间戳间隙
};
```

## 更新日志

### v1.0.0 (当前版本)
- ✅ 添加 FLV 格式支持
- ✅ 自动视频格式检测
- ✅ 统一播放控制接口
- ✅ 错误恢复机制
- ✅ 资源自动清理
- ✅ 视频格式显示标签

## 技术支持

如果遇到问题，请：

1. 查看浏览器控制台错误信息
2. 确认 flv.js 依赖已正确安装
3. 验证视频 URL 格式和可访问性
4. 检查网络连接和防火墙设置

## 相关文档

- [flv.js 官方文档](https://github.com/bilibili/flv.js)
- [HLS.js 官方文档](https://github.com/video-dev/hls.js)
- [组件详细说明](./src/views/plan/components/README_FLV_SUPPORT.md)
