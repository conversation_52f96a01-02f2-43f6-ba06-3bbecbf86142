<template>
  <div class="flex-grow  mx-auto py-6 px-6">
      <!-- 巡更控制区 -->
      <div class="bg-white rounded-xl p-6 mb-6 card-shadow">
      <div class="flex flex-col md:flex-row md:items-center justify-between">
        <div class="mb-4 md:mb-0">
          <h2 class="text-lg font-semibold mb-1">巡更统计</h2>
          <p class="text-gray-500 text-sm">实时监控巡更状态和设备运行情况</p>
        </div>

        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 rounded-full  animate-pulse" :class="[isPatrolActive ? 'bg-success' : 'bg-danger']"></div>
            <span class="text-sm">{{ isPatrolActive ? "白天巡更已开启" : "白天巡更暂未开启"}}</span>
          </div>

          <button @click="togglePatrol" class="px-5 py-2.5 rounded-lg bg-primary text-white font-medium hover:bg-primary/90 transition-colors flex items-center space-x-2">
    
            <Icon :icon="isPatrolActive ? 'tabler:player-pause-filled' : 'tabler:player-play-filled'" width="20"></Icon>
            <span>{{ isPatrolActive ? '关闭巡更' : '开启巡更' }}</span>
          </button>
        </div>
      </div>
    </div>
      
      <!-- 数据概览卡片 -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6 mb-6">
        <!-- 路线条数 -->
        <div class="bg-white rounded-xl p-6 card-shadow card-hover">
        <div class="flex items-start justify-between">
          <div>
            <p class="text-gray-500 text-sm mb-1">路线条数</p>
            <h3 class="text-3xl font-bold cursor-pointer hover:text-primary transition-colors" @click="navigateTo('routes')">{{ formatNumber(routeCount) }}</h3>
            <p class="text-xs mt-2 flex items-center" :class="[countInfo.lineIncrease >= 0 ? 'text-success' : 'text-danger']" >
              <Icon icon="mdi:arrow-up" size="14" class="mr-1"></Icon>
              <span>较昨日 <span class="font-medium">{{ countInfo.lineIncrease }}</span></span>
            </p>
          </div>
          <div class="w-12 h-12 rounded-lg bg-primary-light flex items-center justify-center">
            <Icon icon="gis:route" size="24" class="text-primary"></Icon>
          </div>
        </div>
      </div>
        
        <!-- 总巡检记录 -->
        <div class="bg-white rounded-xl p-6 card-shadow card-hover">
        <div class="flex items-start justify-between">
          <div>
            <p class="text-gray-500 text-sm mb-1">总巡更记录</p>
            <h3 class="text-3xl font-bold cursor-pointer hover:text-primary transition-colors" @click="navigateTo('records')">{{ formatNumber(inspectionCount) }}</h3>
            <p class="text-xs mt-2 flex items-center" :class="[countInfo.recordIncrease >= 0 ? 'text-success' : 'text-danger']">
              <Icon :icon="countInfo.recordIncrease >= 0 ? 'mdi:arrow-up' : 'mdi:arrow-down'" size="14" class="mr-1"></Icon>
              <span>较昨日 <span class="font-medium">{{ countInfo.recordIncrease }}</span></span>
            </p>
          </div>
          <div class="w-12 h-12 rounded-lg bg-primary-light flex items-center justify-center">
            <Icon icon="material-symbols:receipt-long-outline-rounded" size="24" class="text-primary"></Icon>
          </div>
        </div>
      </div>
        
        <!-- 服刑人员数量统计 -->
        <div class="bg-white rounded-xl p-6 card-shadow card-hover">
          <div class="flex items-start justify-between">
            <div>
              <p class="text-gray-500 text-sm mb-1">服刑人员总数</p>
              <h3 class="text-3xl font-bold cursor-pointer hover:text-primary transition-colors" @click="navigateTo('prisoners')">{{ formatNumber(prisonerCount) }}</h3>
              <p class="text-xs mt-2 flex items-center" :class="[countInfo.patrolUserIncrease >= 0 ? 'text-success' : 'text-danger']">
                <Icon :icon="countInfo.patrolUserIncrease >= 0 ? 'mdi:arrow-up' : 'mdi:arrow-down'" size="14" class="mr-1"></Icon>
                <span>较昨日 <span class="font-medium">{{ countInfo.patrolUserIncrease || 0 }}</span></span>
              </p>
            </div>
            <div class="w-12 h-12 rounded-lg bg-warning-light flex items-center justify-center">
              <Icon icon="material-symbols:group" size="24" class="text-warning"></Icon>
            </div>
          </div>
        </div>

      <!-- 巡更设备总数 -->
      <div class="bg-white rounded-xl p-6 card-shadow card-hover">
          <div class="flex items-start justify-between">
            <div>
              <p class="text-gray-500 text-sm mb-1">巡更设备总数</p>
              <h3 class="text-3xl font-bold cursor-pointer hover:text-primary transition-colors" @click="navigateTo('devices')">{{ patrolDeviceCount }}</h3>
              <div class="flex items-center mt-2">
                <div class="flex items-center mr-4">
                  <div class="w-2 h-2 rounded-full bg-success mr-1"></div>
                  <span class="text-xs">在线: <span class="font-medium">{{ onlinePatrolDevices }}</span></span>
                </div>
                <div class="flex items-center">
                  <div class="w-2 h-2 rounded-full bg-danger mr-1"></div>
                  <span class="text-xs">离线: <span class="font-medium">{{ offlinePatrolDevices }}</span></span>
                </div>
              </div>
            </div>
            <div class="w-12 h-12 rounded-lg bg-primary-light flex items-center justify-center">
              <Icon icon="material-symbols:perm-device-information" size="24" class="text-primary"></Icon>
            </div>
          </div>
        </div>
        
        <!-- 报警灯总数 -->
        <div class="bg-white rounded-xl p-6 card-shadow card-hover">
          <div class="flex items-start justify-between">
            <div>
              <p class="text-gray-500 text-sm mb-1">报警灯总数</p>
              <h3 class="text-3xl font-bold cursor-pointer hover:text-primary transition-colors" @click="navigateTo('alarms')">{{ alarmLightCount }}</h3>
              <div class="flex items-center mt-2">
                <div class="flex items-center mr-4">
                  <div class="w-2 h-2 rounded-full bg-success mr-1"></div>
                  <span class="text-xs">在线: <span class="font-medium">{{ onlineAlarmLights }}</span></span>
                </div>
                <div class="flex items-center">
                  <div class="w-2 h-2 rounded-full bg-danger mr-1"></div>
                  <span class="text-xs">离线: <span class="font-medium">{{ offlineAlarmLights }}</span></span>
                </div>
              </div>
            </div>
            <div class="w-12 h-12 rounded-lg bg-primary-light flex items-center justify-center">
              <Icon icon="material-symbols:assistant-device" size="24" class="text-primary"></Icon>
            </div>
          </div>
        </div>


        <!-- 巡更点数 -->
        <div class="bg-white rounded-xl p-6 card-shadow card-hover">
          <div class="flex items-start justify-between">
            <div>
              <p class="text-gray-500 text-sm mb-1">巡更点数</p>
              <h3 class="text-3xl font-bold cursor-pointer hover:text-primary transition-colors" @click="navigateTo('points')">{{ patrolPointCount }}</h3>
              <p class="text-xs mt-2 flex items-center" :class="[countInfo.patrolPointIncrease >= 0 ? 'text-success' : 'text-danger']">
                <Icon :icon="countInfo.patrolPointIncrease >= 0 ? 'mdi:arrow-up' : 'mdi:arrow-down'" size="14" class="mr-1"></Icon>
                <span>较昨日 <span class="font-medium">{{ countInfo.patrolPointIncrease }}</span></span>
              </p>
            </div>
            <div class="w-12 h-12 rounded-lg bg-primary-light flex items-center justify-center">
              <Icon icon="mdi:map-marker" size="24" class="text-primary"></Icon>
            </div>
          </div>
        </div>

         <!-- 巡检合格数 -->
         <div class="bg-white rounded-xl p-6 card-shadow card-hover">
        <div class="flex items-start justify-between">
          <div>
            <p class="text-gray-500 text-sm mb-1">巡更合格数</p>
            <h3 class="text-3xl font-bold cursor-pointer hover:text-primary transition-colors" @click="navigateTo('qualified')">{{ formatNumber(qualifiedCount) }}</h3>
            <p class="text-success text-xs mt-2 flex items-center">
              <Icon icon="mdi:arrow-up" size="14" class="mr-1"></Icon>
              <span>合格率 <span class="font-medium">{{ countInfo.passRate }}</span></span>
            </p>
          </div>
          <div class="w-12 h-12 rounded-lg bg-success-light flex items-center justify-center">
            <Icon icon="mdi:check-circle" size="24" class="text-success"></Icon>
          </div>
        </div>
      </div>
        
        <!-- 待巡数 -->
        <div class="bg-white rounded-xl p-6 card-shadow card-hover">
        <div class="flex items-start justify-between">
          <div>
            <p class="text-gray-500 text-sm mb-1">待巡数</p>
            <h3 class="text-3xl font-bold cursor-pointer hover:text-primary transition-colors" @click="navigateTo('pending')">{{ pendingCount }}</h3>
            <p class="text-warning text-xs mt-2 flex items-center">
              <Icon icon="mdi:alert-circle" size="14" class="mr-1"></Icon>
              <span>今日剩余 <span class="font-medium">{{ countInfo.todayPendingPlans }}</span> 个点位</span>
            </p>
          </div>
          <div class="w-12 h-12 rounded-lg bg-warning-light flex items-center justify-center">
            <Icon icon="mdi:clock-outline" size="24" class="text-warning"></Icon>
          </div>
        </div>
      </div>
        
        <!-- 漏检数 -->
        <div class="bg-white rounded-xl p-6 card-shadow card-hover">
          <div class="flex items-start justify-between">
            <div>
              <p class="text-gray-500 text-sm mb-1">漏检数</p>
              <h3 class="text-3xl font-bold cursor-pointer hover:text-primary transition-colors" @click="navigateTo('missed')">{{ missedCount }}</h3>
              <p class="text-xs mt-2 flex items-center" :class="[countInfo.missedCardsIncrease >= 0 ? 'text-success' : 'text-danger']">
                <Icon :icon="countInfo.missedCardsIncrease >= 0 ? 'mdi:arrow-up' : 'mdi:arrow-down'" size="14" class="mr-1"></Icon>
                <span>较昨日 <span class="font-medium">{{ countInfo.missedCardsIncrease }}</span></span>
              </p>
            </div>
            <div class="w-12 h-12 rounded-lg bg-danger-light flex items-center justify-center">
              <Icon icon="mdi:close-circle" size="24" class="text-danger"></Icon>
            </div>
          </div>
      </div>
      <!-- 人员卡数量 -->
      <div class="bg-white rounded-xl p-6 card-shadow card-hover">
        <div class="flex items-start justify-between">
          <div>
            <p class="text-gray-500 text-sm mb-1">人员卡数量</p>
            <h3 class="text-3xl font-bold cursor-pointer hover:text-primary transition-colors" @click="navigateTo('personnelCards')">{{ formatNumber(personnelCardCount) }}</h3>
            <p class="text-xs mt-2 flex items-center" :class="[countInfo.userCardIncrease >= 0 ? 'text-success' : 'text-danger']">
              <Icon :icon="countInfo.userCardIncrease >= 0 ? 'mdi:arrow-up' : 'mdi:arrow-down'" size="14" class="mr-1"></Icon>
              <span>较昨日 <span class="font-medium">{{ countInfo.userCardIncrease || 0 }}</span></span>
            </p>
          </div>
          <div class="w-12 h-12 rounded-lg bg-primary-light flex items-center justify-center">
            <Icon icon="mdi:card-account-details" size="24" class="text-primary"></Icon>
          </div>
        </div>
      </div>

      </div>
      
      <!-- 图表区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- 巡检完成情况图表 -->
        <div class="bg-white rounded-xl p-6 card-shadow">
          <div class="flex items-center justify-between mb-6">
            <h3 class="font-semibold text-lg">巡更完成情况</h3>
            <div class="flex space-x-2">
              <button 
                  @click="selectedTimeRange = 'today'; getCompletionCount('today')"
                  :class="{
                    'bg-primary text-white': selectedTimeRange === 'today',
                    'bg-gray-100 text-gray-500 hover:bg-gray-200 transition-colors': selectedTimeRange !== 'today'
                  }"
                  class="px-3 py-1 text-xs rounded-md"
                >
                  今日
                </button>
                
                <button 
                  @click="selectedTimeRange = 'week'; getCompletionCount('week')"
                  :class="{
                    'bg-primary text-white': selectedTimeRange === 'week',
                    'bg-gray-100 text-gray-500 hover:bg-gray-200 transition-colors': selectedTimeRange !== 'week'
                  }"
                  class="px-3 py-1 text-xs rounded-md"
                >
                  本周
                </button>

                <button 
                  @click="selectedTimeRange = 'month'; getCompletionCount('month')"
                  :class="{
                    'bg-primary text-white': selectedTimeRange === 'month',
                    'bg-gray-100 text-gray-500 hover:bg-gray-200 transition-colors': selectedTimeRange !== 'month'
                  }"
                  class="px-3 py-1 text-xs rounded-md"
                >
                  本月
                </button>
            </div>
          </div>
          <div class="h-80">
            <div ref="inspectionChart" class="w-full h-full"></div>
          </div>
        </div>
        
         <!-- 实时巡检数据 -->
         <div class="bg-white rounded-xl p-6 card-shadow">
          <div class="flex items-center justify-between mb-6">
            <h3 class="font-semibold text-lg">实时巡更数据</h3>
            <div class="flex items-center space-x-2">
              <span class="text-xs text-gray-500">最后更新: <span>{{ lastUpdateTime }}</span></span>
              <div class="w-2 h-2 rounded-full bg-success animate-pulse"></div>
              <!-- 自动刷新状态 -->
              <div class="auto-refresh-badge">
                <Icon icon="mdi:refresh" class="refresh-icon" :class="{'refreshing': isRefreshing}" />
                <span class="refresh-text">{{ isRefreshing ? '刷新中' : '自动刷新' }}</span>
              </div>
              <!-- 新数据提示 -->
              <div v-if="showNewDataBadge" class="new-data-badge" @animationend="showNewDataBadge = false">
                新数据
              </div>
            </div>
          </div>
          <div class="h-80 overflow-y-auto scrollbar-hide space-y-2 relative">
            <!-- 新数据指示线 -->
            <!-- <div v-if="hasNewData" class="new-data-indicator">
              <div class="new-data-line"></div>
              <span class="new-data-text">新数据</span>
              <div class="new-data-line"></div>
            </div> -->
            
            <TransitionGroup name="data-list" tag="div" class="space-y-2">
              <div v-for="(point, index) in realtimeData" :key="point.id || index" 
                   class="tech-card relative p-3 rounded-lg border border-primary/10 tech-hover compact-card"
                   :class="{'tech-fade-in': point.isNew}">
                <div class="tech-glow absolute inset-0 rounded-lg" :class="{'tech-glow-active': point.isNew}"></div>
                <div class="relative z-10 flex items-center">
                  <!-- 头像和图标区域 -->
                  <div class="flex items-center mr-3">
                    <!-- <div class="tech-icon-container compact-icon" :class="{'pulse-icon': point.isNew}">
                      <Icon icon="tabler:bell-filled" class="text-primary tech-icon" style="display: block;"></Icon>
                    </div> -->
                    <div class="avatar-wrapper compact-avatar ml-2" :class="{'avatar-pulse': point.isNew}">
                      <template v-if="point.patrolUserImage">
                        <img 
                          :src="getFileAccessHttpUrl(point.patrolUserImage)" 
                          :alt="point.patrolUserName || '未知'"
                          class="avatar-image"
                          @error="handleAvatarError"
                        >
                      </template>
                      <template v-else>
                        <div class="avatar-icon-container">
                          <Icon icon="mdi:account" class="avatar-icon text-primary" />
                        </div>
                      </template>
                      <div class="avatar-status compact-status"></div>
                    </div>
                  </div>
                  
                  <!-- 巡更人信息 -->
                  <div class="mr-4 min-w-[80px]">
                    <h4 class="font-medium text-xs text-primary">{{ point.patrolUserName || '未知' }}</h4>
                  </div>
                  
                  <!-- 巡更卡片名称 -->
                  <div class="mr-4 flex-grow">
                    <div class="flex items-center">
                      <Icon icon="mdi:map-marker" class="mr-1 text-primary text-xs" />
                      <span class="truncate max-w-[120px] text-xs">{{ point.cardName }}</span>
                    </div>
                  </div>
                  
                  <!-- 巡更路线 -->
                  <div class="mr-4 min-w-[120px]">
                    <div class="flex items-center">
                      <Icon icon="mingcute:route-fill" class="mr-1 text-primary text-xs" />
                      <span class="truncate max-w-[100px] text-xs">{{ point.lineName || '未知路线' }}</span>
                    </div>
                  </div>
                  
                  <!-- 巡更设备 -->
                  <div class="mr-4 min-w-[100px]">
                    <div class="flex items-center">
                      <Icon icon="mdi:cellphone" class="mr-1 text-primary text-xs" />
                      <span class="truncate max-w-[80px] text-xs">{{ point.deviceName }}</span>
                    </div>
                  </div>
                  
                  <!-- 巡更时间 -->
                  <div class="text-right ml-auto">
                    <span class="tech-time px-2 py-1 rounded text-xs whitespace-nowrap">{{ point.recordTime }}</span>
                    <div class="tech-badge mt-2" :class="{'tech-badge-active': point.isNew}"></div>
                  </div>
                </div>
              </div>
            </TransitionGroup>
            
            <!-- 空状态展示 -->
            <div v-if="realtimeData.length === 0" class="flex flex-col items-center justify-center h-full text-gray-400">
              <Icon icon="mdi:database-off" size="48" class="mb-2 text-gray-300" />
              <p>暂无巡更数据</p>
            </div>
          </div>
        </div>
      </div>
      
      
      
      <!-- 漏检路线排行榜和合格路线排行榜 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- 漏检路线排行榜 -->
        <div class="bg-white rounded-xl p-6 card-shadow">
          <div class="flex items-center justify-between mb-6">
            <h3 class="font-semibold text-lg">巡更漏检路线排行榜</h3>
            <div class="flex space-x-2">
              <button 
                  @click="leakTimeRange = 'today'; getleakCount('today')"
                  :class="{
                    'bg-primary text-white': leakTimeRange === 'today',
                    'bg-gray-100 text-gray-500 hover:bg-gray-200 transition-colors': leakTimeRange !== 'today'
                  }"
                  class="px-3 py-1 text-xs rounded-md"
                >
                  今日
                </button>
                
                <button 
                  @click="leakTimeRange = 'week'; getleakCount('week')"
                  :class="{
                    'bg-primary text-white': leakTimeRange === 'week',
                    'bg-gray-100 text-gray-500 hover:bg-gray-200 transition-colors': leakTimeRange !== 'week'
                  }"
                  class="px-3 py-1 text-xs rounded-md"
                >
                  本周
                </button>

                <button 
                  @click="leakTimeRange = 'month'; getleakCount('month')"
                  :class="{
                    'bg-primary text-white': leakTimeRange === 'month',
                    'bg-gray-100 text-gray-500 hover:bg-gray-200 transition-colors': leakTimeRange !== 'month'
                  }"
                  class="px-3 py-1 text-xs rounded-md"
                >
                  本月
                </button>
            </div>
          </div>
          <div class="rank-list-container h-80 overflow-y-auto scrollbar-hide">
            <div class="space-y-4">
              <template v-if="missedList.length > 0">
                <div v-for="(route, index) in missedList" :key="index"
                    :class="`flex items-center p-3 rounded-lg tech-rank-item ${index === 0 ? 'rank-first' : index === 1 ? 'rank-second' : index === 2 ? 'rank-third' : 'rank-normal'}`">
                  <div :class="`w-8 h-8 rounded-full ${index === 0 ? 'bg-danger' : index === 1 ? 'bg-danger/80' : index === 2 ? 'bg-danger/60' : 'bg-gray-300'} flex items-center justify-center text-white mr-4 font-medium`">
                    {{ index + 1 }}
                  </div>
                  <div class="flex-grow">
                    <h4 class="font-medium text-sm">{{ route.lineName }}</h4>
                    <p class="text-xs text-gray-500">漏检点位: {{ route.missedCount }}个</p>
                  </div>
                  <span class="text-xs" :class="index < 2 ? 'text-danger' : index === 2 ? 'text-danger/80' : 'text-gray-500'">
                    {{ route.level }}
                  </span>
                </div>
              </template>

              <template v-else>
                <div class="flex flex-col items-center justify-center py-6 text-gray-400 pt30">
                  <a-empty />
                </div>
              </template>
            </div>
          </div>
        </div>
        
        <!-- 合格路线排行榜 -->
        <div class="bg-white rounded-xl p-6 card-shadow">
          <div class="flex items-center justify-between mb-6">
            <h3 class="font-semibold text-lg">巡更合格路线排行榜</h3>
            <div class="flex space-x-2">
              <button 
                  @click="qualifiedTimeRange = 'today'; getqualifiedCountList('today')"
                  :class="{
                    'bg-primary text-white': qualifiedTimeRange === 'today',
                    'bg-gray-100 text-gray-500 hover:bg-gray-200 transition-colors': qualifiedTimeRange !== 'today'
                  }"
                  class="px-3 py-1 text-xs rounded-md"
                >
                  今日
                </button>
                
                <button 
                  @click="qualifiedTimeRange = 'week'; getqualifiedCountList('week')"
                  :class="{
                    'bg-primary text-white': qualifiedTimeRange === 'week',
                    'bg-gray-100 text-gray-500 hover:bg-gray-200 transition-colors': qualifiedTimeRange !== 'week'
                  }"
                  class="px-3 py-1 text-xs rounded-md"
                >
                  本周
                </button>

                <button 
                  @click="qualifiedTimeRange = 'month'; getqualifiedCountList('month')"
                  :class="{
                    'bg-primary text-white': qualifiedTimeRange === 'month',
                    'bg-gray-100 text-gray-500 hover:bg-gray-200 transition-colors': qualifiedTimeRange !== 'month'
                  }"
                  class="px-3 py-1 text-xs rounded-md"
                >
                  本月
                </button>
            </div>
          </div>
          <div class="rank-list-container h-80 overflow-y-auto scrollbar-hide">
            <div class="space-y-4">
              <template v-if="qualifiedList.length > 0">
              <div v-for="(route, index) in qualifiedList" :key="index" 
                   :class="`flex items-center p-3 rounded-lg tech-rank-item ${index === 0 ? 'rank-first-success' : index === 1 ? 'rank-second-success' : index === 2 ? 'rank-third-success' : 'rank-normal-success'}`">
                <div :class="`w-8 h-8 rounded-full ${index === 0 ? 'bg-success' : index === 1 ? 'bg-success/80' : index === 2 ? 'bg-success/60' : index === 3 ? 'bg-success/40' : 'bg-success/20'} flex items-center justify-center text-white mr-4 font-medium`">
                  {{ index + 1 }}
                </div>
                <div class="flex-grow">
                  <h4 class="font-medium text-sm">{{ route.lineName }}</h4>
                  <p class="text-xs text-gray-500">合格率: {{ route.passRate }}%</p>
                </div>
                <span class="text-xs" :class="index === 0 ? 'text-success' : index === 1 ? 'text-success' : index === 2 ? 'text-success/80' : index === 3 ? 'text-success/60' : 'text-success/40'">
                  {{ route.level }}
                </span>
              </div>
              </template>
              <template v-else>
                <div class="flex flex-col items-center justify-center py-6 text-gray-400 pt30">
                  <a-empty />
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>

      <!-- 民警巡更记录 -->
      <div class="bg-white rounded-xl p-6 card-shadow mb-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="font-semibold text-lg">民警巡更记录</h3>
          <a  class="text-primary text-sm flex items-center" @click="goMore">
            <span>查看全部</span>
            <Icon icon="mdi:chevron-right" class="fa fa-angle-right ml-1"></Icon>
          </a>
        </div>
        <div class="overflow-x-auto scrollbar-hide">
          <table class="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">巡更路线</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">巡更点</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">巡更时间</th>
               
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="(record, index) in policeList" :key="index">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">{{ record.lineName }}</div>
                     
                    </div>
                  </div>
                </td>
               
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ record.cradName || '--' }}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 " >
                    {{ record.recordTime }}
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

  </div>
</template>
<script lang="ts" setup>
import * as echarts from 'echarts';
import { nextTick, onMounted, onUnmounted, ref } from 'vue';
import { Icon } from '/@/components/Icon';
import { deviceCount, completionCount, leakCount, qualifiedCountList, getRealtimePatrolData, getPatrolEnabled, saveOpenClose, getPoliceListUrl } from '../api';
import { useMessage } from '/@/hooks/web/useMessage';
import { columns } from '/@/views/alarm/Alarm.data';
import { useRouter } from 'vue-router';
import { getFileAccessHttpUrl } from '/@/utils/common/compUtils';



const { notification } = useMessage();
const router = useRouter();
 
 // 状态管理
const isPatrolActive = ref(false);
const routeCount = ref(0);
const inspectionCount = ref(0);
const qualifiedCount = ref(0);
const pendingCount = ref(0);
const missedCount = ref(0);
const patrolDeviceCount = ref(0);
const onlinePatrolDevices = ref(0);
const offlinePatrolDevices = ref(0);
const alarmLightCount = ref(0);
const onlineAlarmLights = ref(0);
const offlineAlarmLights = ref(0);
const patrolPointCount = ref(0);
const prisonerCount = ref(0);
const lastUpdateTime = ref("刚刚");
const inspectionChart = ref(null);
const chartInstance = ref(null);
const countInfo = ref([]);
const selectedTimeRange =  ref('today');
const leakTimeRange = ref('today');
const qualifiedTimeRange = ref('today');
const missedList = ref([]);
const qualifiedList = ref([]);
const realtimeData = ref([]);
const policeList = ref([]);
const personnelCardCount = ref(0);
const hasNewData = ref(false);
const showNewDataBadge = ref(false);
const isRefreshing = ref(false);
let realtimeInterval = null;

/**
 * 查看更多民警巡更记录
 */
function goMore(){
  // 跳转到巡更记录页面
  router.push('/inspection/record');
}

/**
 * 点击统计数字导航到对应页面
 * @param type 导航类型：routes-路线, records-记录, qualified-合格, pending-待巡, missed-漏检
 */
function navigateTo(type) {
  switch(type) {
    case 'routes':
      router.push('/line/lineList');
      break;
    case 'records':
      router.push('/record/recordList');
      break;
    case 'qualified':
      router.push('/planCard/planCardList?status=1');
      break;
    case 'pending':
      router.push('/planCard/planCardList?status=0');
      break;
    case 'missed':
      router.push('/planCard/planCardList?status=2');
      break;
    case 'devices':
      router.push('/device/deviceList');
      break;
    case 'alarms':
      router.push('/alarmLightDevice/alarmLightDeviceList');
      break;
    case 'points':
      router.push('/card/cardList?type=2');
      break;
    case 'prisoners':
      router.push('/patrolUser/patrolUserList');
      break;
    case 'personnelCards':
      router.push('/card/cardList?type=1');
      break;
    default:
      router.push('/dashboard/analysis');
  }
 
}

// 格式化数字（超过10000显示为X.X万）
const formatNumber = (num) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万';
  }
  return num.toString();
};

// 切换巡更状态
const togglePatrol = () => {
  isPatrolActive.value = !isPatrolActive.value;

  // 调用接口保存巡更状态
  saveOpenClose({ isOpen: isPatrolActive.value }).then((res) => {
    if (res.success) {
      if (isPatrolActive.value) {
        notification.success({message:'白天巡更已开启',description:'系统已成功启动巡更模式'});
      } else {
        notification.warning({ message:'白天巡更已关闭',description: '系统已停止巡更模式'});
        
      }
    }else {
      console.error('保存巡更状态失败:', res.message);
    }
  });

};



// 数字动画
const animateNumber = (target, start, end, duration) => {
  let startTime = null;
  const step = (timestamp) => {
    if (!startTime) startTime = timestamp;
    const progress = Math.min((timestamp - startTime) / duration, 1);
    target.value = Math.floor(progress * (end - start) + start);
    if (progress < 1) window.requestAnimationFrame(step);
  };
  window.requestAnimationFrame(step);
};

// 启动自动刷新功能
const startAutoRefresh = () => {
  // 清除可能存在的定时器
  if (realtimeInterval) {
    clearInterval(realtimeInterval);
  }
  
  // 立即执行一次数据获取
  getRecordList().finally(() => {
    // 无论成功失败，1秒后隐藏刷新状态
    setTimeout(() => {
      isRefreshing.value = false;
    }, 1000);
  });
  
  // 设置定时器，每10秒获取一次数据
  realtimeInterval = setInterval(() => {
    isRefreshing.value = true; // 显示刷新中状态
    
    // 获取数据
    getRecordList().finally(() => {
      // 无论成功失败，1秒后隐藏刷新状态
      setTimeout(() => {
        isRefreshing.value = false;
      }, 1000);
    });
  }, 10000);
  
  console.log('自动刷新已开启，间隔：10秒');
};

// 修改getRecordList函数返回Promise以便链式调用
function getRecordList() {
  // 调用接口获取实时数据
  return new Promise((resolve, reject) => {
    getRealtimePatrolData().then((res) => {
      if (res.success) {
        lastUpdateTime.value = res.result.nowTime;
        
        // 检查是否有新数据
        if (res.result.recordList && res.result.recordList.length > 0) {
          // 标记新数据并添加唯一ID
          const newData = res.result.recordList.map((item, idx) => ({
            ...item,
            isNew: true,
            id: `new-${Date.now()}-${idx}` // 确保每条记录有唯一ID
          }));
          
          // 只有当确实有新数据时才显示指示器
          if (newData.length > 0) {
            // 显示新数据到达的提示
            showNewDataBadge.value = true;
            hasNewData.value = true;
            
            // 5秒后隐藏新数据分割线
            setTimeout(() => {
              hasNewData.value = false;
            }, 5000);
            
            // 将isNew标志在一段时间后移除
            setTimeout(() => {
              realtimeData.value.forEach((item, idx) => {
                if (idx < newData.length) {
                  item.isNew = false;
                }
              });
            }, 3000);
          }
          
          // 清除之前数据的新标记
          const oldData = realtimeData.value.map(item => ({
            ...item,
            isNew: false
          }));
          
          // 插入新数据到顶部
          realtimeData.value = [...newData, ...oldData];
          
          // 限制最大显示数量
          if (realtimeData.value.length > 10) {
            realtimeData.value = realtimeData.value.slice(0, 10);
          }
        }
        resolve(res);
      } else {
        console.warn('未获取到新的实时数据');
        resolve(null);
      }
    }).catch(err => {
      console.error('获取实时数据失败:', err);
      reject(err);
    });
  });
}

// 初始化图表
onMounted(() => {

  // 获取设备数量数据
  getCount();
  getDayPatrol();//判断白天巡检是否开启
  getPoliceList()
  nextTick(() => {
    getCompletionCount("today")//获取巡检完成情况数据
    getleakCount("today")//获取漏检排行榜
    getqualifiedCountList("today")//获取合格路线排行榜
    
    // 启动自动刷新
    startAutoRefresh();
  });
});

//获取民警巡更数据
function getPoliceList(){
  getPoliceListUrl({
    pageNo: 1,
    pageSize: 10,
    deviceUser:"1",
    column:"recordTime",
    order:"desc"
  }).then(res => {
    if (res.success) {
      policeList.value = res.result.records
    }
  });
}

function getDayPatrol(){
  // 获取巡更是否开启
  getPatrolEnabled().then((res) => {
      if (res.success) {
        isPatrolActive.value = res.result
        
      } else {
        console.error('获取巡更状态失败:', res.message);
      }
    });
}

/**
 * 获取漏检排行榜
 * @param type 
 */
function getleakCount(type){
  leakCount({ timeRange: type }).then((res) => {
      if (res.success) {
        missedList.value = res.result;
      }
    });
}

/**
 * 获取合格排行榜
 * @param type 
 */
function getqualifiedCountList(type){
  qualifiedCountList({ timeRange: type }).then((res) => {
      if (res.success) {
        qualifiedList.value = res.result;
      }
    });
}

// 获取巡检完成情况数据
function getCompletionCount(type){
  if (inspectionChart.value) {
      
      chartInstance.value = echarts.init(inspectionChart.value);
      //接口获取数据
      completionCount({ timeRange: type }).then(res => {
          if(res.success){
            chartInstance.value.setOption({
                tooltip: { trigger: 'axis' },
                legend: { data: ['计划巡更数', '实际巡更数', '异常数'] },
                xAxis: { type: 'category', data: res.result.labels },
                yAxis: { type: 'value' },
                series: [
                  { name: '计划巡更数', type: 'line', data: res.result.totalPlans},
                  { name: '实际巡更数', type: 'line', data: res.result.completedCount },
                  { name: '异常数', type: 'line', data: res.result.abnormalCount }
                ]
              });

              window.addEventListener('resize', () => {
                if (chartInstance.value) chartInstance.value.resize();
              });
          }else{
            console.error('获取巡检完成情况数据:', res.message);
          }
          
        })
    }
}

/**
 * 统计数据
 */
function getCount(){
  deviceCount().then(res => {
    if(res.success){
        countInfo.value = res.result;
        
         // 初始化数据
        animateNumber(routeCount, 0, res.result.lineTotalCount, 1500);
        animateNumber(inspectionCount, 0, res.result.totalRecordCount, 1500);
        animateNumber(qualifiedCount, 0, res.result.qualifiedRecords, 1500);
        animateNumber(pendingCount, 0, res.result.pendingPlans, 1500);
        animateNumber(missedCount, 0, res.result.missedCards, 1500);
        animateNumber(patrolDeviceCount, 0, res.result.deviceTotalCount, 1500);
        animateNumber(onlinePatrolDevices, 0, res.result.deviceOnlineCount, 1500);
        animateNumber(offlinePatrolDevices, 0, res.result.deviceOfflineCount, 1500);
        animateNumber(alarmLightCount, 0, res.result.alarmLightTotal, 1500);
        animateNumber(onlineAlarmLights, 0, res.result.alarmLightOnline, 1500);
        animateNumber(offlineAlarmLights, 0, res.result.alarmLightOffline, 1500);
        animateNumber(patrolPointCount, 0, res.result.patrolPointTotal, 1500);
        animateNumber(prisonerCount, 0, res.result.totalPatrolUser || 0, 1500);
        animateNumber(personnelCardCount, 0, res.result.totalUserCard || 0, 1500);
    }else{
      console.error('获取设备数量失败:', res.message);
    }
    
  })
}

// 头像加载失败处理
function handleAvatarError(e) {
  // 获取父元素的引用
  const parent = e.target.closest('.avatar-wrapper');
  if (parent) {
    // 移除所有子元素
    while (parent.firstChild) {
      parent.removeChild(parent.firstChild);
    }
    
    // 创建图标容器
    const iconContainer = document.createElement('div');
    iconContainer.className = 'avatar-icon-container';
    
    // 添加一个简单的图标作为替代
    iconContainer.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="avatar-icon" viewBox="0 0 24 24"><path fill="#165DFF" d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>';
    
    parent.appendChild(iconContainer);
    
    // 重新添加状态指示器
    const statusIndicator = document.createElement('div');
    statusIndicator.className = 'avatar-status';
    parent.appendChild(statusIndicator);
  }
}

// 组件卸载时清理
onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
  
  // 清理实时数据更新定时器
  if (realtimeInterval) {
    clearInterval(realtimeInterval);
    console.log('自动刷新已停止');
  }
});
</script>
<style scoped lang="less">
/* 颜色变量 */
:root {
  --primary: #165DFF;
  --primary-light: rgba(22, 93, 255, 0.1);
  --success: #00B42A;
  --success-light: rgba(0, 180, 42, 0.1);
  --warning: #FF7D00;
  --warning-light: rgba(255, 125, 0, 0.1);
  --danger: #F53F3F;
  --danger-light: rgba(245, 63, 63, 0.1);
  --dark: #1D2129;
  --gray-500: #86909C;
  --gray-200: #F2F3F5;
}

.gradient-bg {
  background: linear-gradient(135deg, #165DFF 0%, #4080FF 100%);
}

.card-shadow {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(22, 93, 255, 0.12);
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

button {
  transition: all 0.2s ease;
}

tr:nth-child(even) {
  background-color: #f8fafc;
}

a {
  transition: all 0.2s ease;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@media (max-width: 768px) {
  .hidden-md {
    display: none;
  }

  .grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

@media (max-width: 640px) {
  .grid-cols-2,
  .grid-cols-4 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

.bg-primary-light {
    --tw-bg-opacity: 1;
    background-color: rgb(232 243 255 / var(--tw-bg-opacity, 1));
}
.text-primary {
    --tw-text-opacity: 1;
    color: rgb(22 93 255 / var(--tw-text-opacity, 1));
}
.bg-success-light {
    --tw-bg-opacity: 1;
    background-color: rgb(232 255 243 / var(--tw-bg-opacity, 1));
}
.bg-warning-light {
    --tw-bg-opacity: 1;
    background-color: rgb(255 247 232 / var(--tw-bg-opacity, 1));
}
.bg-danger-light {
    --tw-bg-opacity: 1;
    background-color: rgb(255 232 232 / var(--tw-bg-opacity, 1));
}
.bg-primary {
    --tw-bg-opacity: 1;
    background-color: rgb(22 93 255 / var(--tw-bg-opacity, 1));
}
.text-success {
    --tw-text-opacity: 1;
    color: rgb(0 180 42 / var(--tw-text-opacity, 1));
}
.text-warning {
    --tw-text-opacity: 1;
    color: rgb(255 125 0 / var(--tw-text-opacity, 1));
}
.text-danger {
    --tw-text-opacity: 1;
    color: rgb(245 63 63 / var(--tw-text-opacity, 1));
}
.bg-danger {
    --tw-bg-opacity: 1;
    background-color: rgb(245 63 63 / var(--tw-bg-opacity, 1));
}
.bg-success {
    --tw-bg-opacity: 1;
    background-color: rgb(0 180 42 / var(--tw-bg-opacity, 1));
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-pointer:hover {
  text-decoration: none;
  transform: scale(1.02);
}

/* 科技感样式 */
.tech-card {
  background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(243,247,255,0.8) 100%);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(22, 93, 255, 0.08);
}

.tech-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(22, 93, 255, 0.15);
  border-color: rgba(22, 93, 255, 0.3);
}

.tech-glow {
  pointer-events: none;
  background: radial-gradient(circle at 50% 50%, rgba(22, 93, 255, 0.07), transparent 70%);
}

.tech-fade-in {
  animation: techFadeIn 0.8s ease forwards;
}

.tech-icon-container {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, rgba(22, 93, 255, 0.1) 0%, rgba(22, 93, 255, 0.2) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 10px rgba(22, 93, 255, 0.15);
}

.tech-icon {
  width: 20px;
  height: 20px;
}

.tech-text {
  color: #4e5969;
}

.tech-time {
  background: rgba(22, 93, 255, 0.1);
  color: #165DFF;
  font-weight: 500;
}

.tech-badge {
  width: 40px;
  height: 3px;
  background: linear-gradient(to right, #00b42a, #165DFF);
  margin-left: auto;
  border-radius: 1.5px;
}

.avatar-wrapper {
  position: relative;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid rgba(22, 93, 255, 0.2);
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-status {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 8px;
  height: 8px;
  background-color: var(--success);
  border-radius: 50%;
  border: 1px solid white;
}

@keyframes techFadeIn {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动条美化 */
.scrollbar-hide::-webkit-scrollbar {
  width: 4px;
}

.scrollbar-hide::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-hide::-webkit-scrollbar-thumb {
  background: rgba(22, 93, 255, 0.1);
  border-radius: 2px;
}

.scrollbar-hide::-webkit-scrollbar-thumb:hover {
  background: rgba(22, 93, 255, 0.2);
}

.avatar-icon-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(22, 93, 255, 0.05) 0%, rgba(22, 93, 255, 0.15) 100%);
}

.avatar-icon {
  width: 16px;
  height: 16px;
}
/* 排行榜样式 */
.rank-list-container {
  position: relative;
}

.tech-rank-item {
  transition: all 0.3s ease;
  background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,249,250,0.8) 100%);
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.tech-rank-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.rank-first {
  background: linear-gradient(135deg, rgba(255,240,240,0.9) 0%, rgba(255,235,235,0.8) 100%);
  border-left: 3px solid var(--danger);
}

.rank-second {
  background: linear-gradient(135deg, rgba(255,245,245,0.9) 0%, rgba(255,240,240,0.8) 100%);
  border-left: 2px solid var(--danger);
}

.rank-third {
  background: linear-gradient(135deg, rgba(255,248,248,0.9) 0%, rgba(255,245,245,0.8) 100%);
  border-left: 1px solid var(--danger);
}

.rank-first-success {
  background: linear-gradient(135deg, rgba(240,255,240,0.9) 0%, rgba(235,255,235,0.8) 100%);
  border-left: 3px solid var(--success);
}

.rank-second-success {
  background: linear-gradient(135deg, rgba(245,255,245,0.9) 0%, rgba(240,255,240,0.8) 100%);
  border-left: 2px solid var(--success);
}

.rank-third-success {
  background: linear-gradient(135deg, rgba(248,255,248,0.9) 0%, rgba(245,255,245,0.8) 100%);
  border-left: 1px solid var(--success);
}
/* 数据列表过渡动画 */
.data-list-enter-active,
.data-list-leave-active {
  transition: all 0.5s ease;
}

.data-list-enter-from {
  opacity: 0;
  transform: translateY(-20px);
}

.data-list-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

.data-list-move {
  transition: transform 0.5s ease;
}

.tech-fade-in {
  animation: techFadeIn 0.8s ease forwards;
}

.tech-glow-active {
  animation: glowPulse 2s ease-in-out infinite;
}

.tech-badge-active {
  animation: badgePulse 2s ease-in-out infinite;
}

.pulse-icon {
  animation: iconPulse 2s ease-in-out infinite;
}

.avatar-pulse {
  animation: avatarPulse 2s ease-in-out infinite;
}

.new-data-badge {
  background: linear-gradient(135deg, var(--primary) 0%, #4080FF 100%);
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 10px;
  animation: badgeAppear 0.5s ease forwards, badgeFade 0.5s ease 2.5s forwards;
}

.new-data-indicator {
  display: flex;
  align-items: center;
  width: 100%;
  margin: 8px 0;
  animation: fadeIn 0.5s ease;
}

.new-data-line {
  flex-grow: 1;
  height: 1px;
  background: linear-gradient(to right, transparent, var(--primary), transparent);
}

.new-data-text {
  padding: 0 10px;
  font-size: 10px;
  color: var(--primary);
  font-weight: 500;
}

@keyframes glowPulse {
  0%, 100% {
    opacity: 0.1;
  }
  50% {
    opacity: 0.3;
  }
}

@keyframes badgePulse {
  0%, 100% {
    background: linear-gradient(to right, var(--success), var(--primary));
    opacity: 0.7;
  }
  50% {
    background: linear-gradient(to right, var(--primary), var(--success));
    opacity: 1;
  }
}

@keyframes iconPulse {
  0%, 100% {
    box-shadow: 0 0 0 rgba(22, 93, 255, 0.4);
  }
  50% {
    box-shadow: 0 0 10px rgba(22, 93, 255, 0.7);
  }
}

@keyframes avatarPulse {
  0%, 100% {
    border-color: rgba(22, 93, 255, 0.2);
  }
  50% {
    border-color: rgba(22, 93, 255, 0.8);
  }
}

@keyframes badgeAppear {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes badgeFade {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 自动刷新相关样式 */
.auto-refresh-badge {
  display: flex;
  align-items: center;
  background: rgba(22, 93, 255, 0.1);
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  color: var(--primary);
}

.refresh-icon {
  width: 12px;
  height: 12px;
  margin-right: 4px;
}

.refreshing {
  animation: spinning 1s linear infinite;
}

@keyframes spinning {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 紧凑列表样式 */
.compact-card {
  min-height: 50px;
}

.compact-icon {
  width: 24px;
  height: 24px;
}

.compact-avatar {
  width: 22px;
  height: 22px;
}

.compact-status {
  width: 6px;
  height: 6px;
}

.tech-icon {
  width: 14px;
  height: 14px;
}

@media (max-width: 1280px) {
  .compact-card {
    flex-direction: column;
  }
  
  .compact-card > div {
    flex-wrap: wrap;
  }
}


</style>