# 视频监控播放组件 FLV 格式支持

## 概述

本次优化为视频监控播放组件添加了对 FLV 格式视频的支持，现在支持以下视频格式：

- **FLV** - Flash Video 格式（新增）
- **HLS** - HTTP Live Streaming (.m3u8)
- **MP4** - MPEG-4 视频格式
- **RTMP** - Real-Time Messaging Protocol
- **RTSP** - Real-Time Streaming Protocol

## 主要改进

### 1. 自动格式检测
组件现在能够自动检测视频URL的格式：
```javascript
// 根据URL自动检测格式
const videoFormat = computed(() => {
  const url = props.videoInfo.videoUrl.toLowerCase();
  if (url.includes('.flv') || url.includes('flv')) return 'flv';
  if (url.includes('.m3u8') || url.includes('hls')) return 'hls';
  if (url.includes('.mp4')) return 'mp4';
  // ... 其他格式
});
```

### 2. FLV 播放器集成
- 动态加载 `flv.js` 库，避免构建时依赖问题
- 提供备用播放方案，确保在没有 flv.js 时不会崩溃
- 支持 FLV 直播流和点播文件

### 3. 统一的播放接口
所有格式使用相同的播放控制接口：
- `startVideo()` - 开始播放
- `stopVideo()` - 停止播放
- `refreshVideo()` - 刷新视频
- `captureFrame()` - 截图

### 4. 视频格式显示
在视频信息栏中显示当前视频格式标签，便于调试和监控。

## 安装依赖

如果需要完整的 FLV 支持，请安装 flv.js：

```bash
npm install flv.js
# 或
yarn add flv.js
# 或
pnpm add flv.js
```

## 使用方法

### 基本用法
```vue
<template>
  <VideoMonitorPlayerModal
    :plan-id="planId"
    :video-info="videoInfo"
    :auto-start="false"
    :show-controls="true"
    @error="onVideoError"
  />
</template>

<script setup>
const videoInfo = {
  id: 'video1',
  name: '监控摄像头1',
  videoUrl: 'http://example.com/stream.flv', // FLV 格式
  streamId: 'stream_001',
  websocketUrl: '/websocket/video/stream_001',
  hlsUrl: '/hls/stream_001/index.m3u8',
  streamType: 'preview'
};
</script>
```

### 支持的 FLV URL 格式
```javascript
// HTTP FLV 直播流
'http://example.com/live/stream.flv'

// HTTPS FLV 直播流
'https://example.com/live/stream.flv'

// 带参数的 FLV 流
'http://example.com/live/stream.flv?token=abc123'
```

## 配置选项

### FLV 播放器配置
组件内部使用以下 FLV 播放器配置：

```javascript
const flvConfig = {
  type: 'flv',
  url: videoUrl,
  isLive: true,
  hasAudio: true,
  hasVideo: true,
  cors: true,
  withCredentials: false
};

const flvOptions = {
  enableWorker: false,
  enableStashBuffer: true,
  stashInitialSize: 128,
  autoCleanupSourceBuffer: true,
  autoCleanupMaxBackwardDuration: 30,
  autoCleanupMinBackwardDuration: 10,
  fixAudioTimestampGap: true,
  accurateSeek: false,
  lazyLoad: true,
  lazyLoadMaxDuration: 180, // 3分钟
  lazyLoadRecoverDuration: 30
};
```

## 错误处理

组件提供完善的错误处理机制：

1. **格式不支持** - 自动降级到备用播放方案
2. **网络错误** - 自动重连机制
3. **播放器错误** - 显示详细错误信息
4. **资源清理** - 组件卸载时自动清理所有资源

## 调试信息

在浏览器控制台中可以看到详细的调试信息：
- 视频格式检测结果
- 播放器初始化状态
- 错误信息和恢复尝试
- 资源清理过程

## 兼容性

- **现代浏览器** - 完全支持 FLV 播放
- **旧版浏览器** - 自动降级到 WebSocket 或其他播放方案
- **移动设备** - 支持移动端 FLV 播放

## 性能优化

1. **动态加载** - flv.js 按需加载，减少初始包大小
2. **资源管理** - 自动清理播放器实例，防止内存泄漏
3. **缓冲优化** - 针对直播流优化的缓冲策略
4. **错误恢复** - 智能重连和错误恢复机制

## 注意事项

1. FLV 格式主要用于直播流，确保服务器支持 FLV 输出
2. 某些防火墙可能阻止 FLV 流，请检查网络配置
3. 移动端播放可能需要用户手动触发（浏览器限制）
4. 建议在生产环境中安装 flv.js 依赖以获得最佳性能

## 故障排除

### 常见问题

1. **FLV 播放失败**
   - 检查是否安装了 flv.js
   - 验证 FLV 流 URL 是否可访问
   - 查看浏览器控制台错误信息

2. **视频无法加载**
   - 检查网络连接
   - 验证视频流是否正常
   - 尝试刷新页面

3. **播放卡顿**
   - 检查网络带宽
   - 调整缓冲配置
   - 考虑降低视频质量

### 日志分析
组件提供详细的日志输出，可以通过浏览器开发者工具查看：
```
[VideoMonitorPlayerModal] 检测到视频格式: flv
[VideoMonitorPlayerModal] 开始启动FLV视频播放
[VideoMonitorPlayerModal] FLV播放成功
```
